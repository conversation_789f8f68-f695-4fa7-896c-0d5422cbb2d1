# 服务器快速部署指南

## 1. 准备工作

### 1.1 相信你已经在本地运行成功了


### 1.2 购买
- Linux 服务器（推荐 Ubuntu 20.04 LTS）
- 开放端口：80(http), 443(https),3306(数据库),7001(ssl部署平台),7002
- 准备一个域名，并解析到服务器IP

### 1.3 相关软件

- 服务器远程连接工具：FinalShell（建议）
- 数据库远程连接工具：Navicat （推荐）

## 2. 项目打包

### 2.1 前端打包
```bash
# 进入admin项目目录
进入CMD

# 构建生产环境代码
npm run build

# 结果
拿到dist文件夹
```

### 2.2 后端打包
```bash
# idea打开项目
使用maven package

# 结果
在admin模块的target里拿到fuu-admin.jar文件
```

## 3. 服务器配置

### 3.1 安装 Docker
[Ubuntu 上安装 docker 并配置 Docker Compose 详细步骤]([Ubuntu 上安装 docker 并配置 Docker Compose 详细步骤_ubuntu安装docker compose-CSDN博客](https://blog.csdn.net/sxc1414749109/article/details/143082699)) 

## 4. 部署配置

### 4.1 目录结构（在opt/app/下创建形成以下结构）

```
opt/app/
├── backend/                	# 后端相关
│   ├── Dockerfile   		    # springboot
│   ├── fuu-admin.jar   		# 项目jar包
│
├── frontend/                   # 前端相关
│   ├── dist/   		   		# 前端dist包
│   ├── ssl/   		   			# https相关
│   	├── cert.key   		   	# 证书文件
│		├── cert.pem   		   	# 证书密钥
│   ├── Dockerfile   		   	# 前端
│   ├── nginx.conf   		   	# nginx配置文件
│
├── certd/                   	# ssl相关
│   ├── docker-conpose.yaml   	# 构建
│
└── docker-compose.yml          # 总构建
```



### 4.2 创建部署目录
```bash
可以在finalshell点击创建
或
可以命令行创建
```

### 4.3 编辑 docker-compose.yml （替换数据库密码）
```yaml
services:
  mysql:
    image: mysql:8  # 阿里云镜像
    environment:
      MYSQL_ROOT_PASSWORD: 换成你的数据密码
      MYSQL_DATABASE: fuudb
      MYSQL_TIMEZONE: Asia/Shanghai
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"  # 暴露主机3306端口映射到容器3306端口
    command:
      --default-time-zone=+08:00
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_network

  redis:
    image: redis:alpine  # 阿里云镜像
    networks:
      - app_network

  backend:
    build: ./backend
    environment:
      TZ: Asia/Shanghai
      SPRING_DATASOURCE_URL: *************************************************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 换成你的数据密码
      SPRING_REDIS_HOST: redis
    ports:
      - "4400:4400"  # 添加WebSocket端口映射
    volumes:
      - ./logs:/app/logs  # 宿主机日志目录挂载
    depends_on:
      - mysql
      - redis
    networks:
      - app_network

  frontend:
    build: ./frontend
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - app_network

volumes:
  mysql_data:

networks:
  app_network:
    driver: bridge
```

### 4.3 配置 nginx.conf（替换域名）
```nginx
server {
    # 监听端口
    listen 80;
    listen 443 ssl;
    server_name 换成你的解析网址;

    # SSL 配置 - 使用容器内的正确路径
    ssl_certificate /usr/share/nginx/ssl/cert.pem;
    ssl_certificate_key /usr/share/nginx/ssl/cert.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # HTTP 重定向到 HTTPS
    if ($scheme = http) {
        return 308 https://$server_name$request_uri;
    }

 	location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /dev/ {
        # 允许所有请求方法通过11111
        proxy_method $request_method;
        
        # 修改rewrite规则，确保正确转发
        rewrite ^/dev/(.*)$ /$1 break;
        proxy_pass http://backend:8081/;  # 注意这里添加了结尾的斜杠
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 添加CORS支持
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, PUT, DELETE';
        add_header Access-Control-Allow-Headers '*';
    }
    # 添加 WebSocket 配置
   location /ws {  # 移除末尾斜杠
        proxy_pass http://backend:4400;  # 确保这里的地址正确
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }
}
```

### 4.4 创建前端Dockerfile

```nginx
FROM nginx:alpine
COPY dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
# 创建ssl目录并复制证书
COPY ssl /usr/share/nginx/ssl/
```

### 4.5 创建后端Dockerfile

```nginx
FROM openjdk:8-jdk
WORKDIR /app
COPY fuu-admin.jar .
EXPOSE 8081
CMD ["java", "-jar", "fuu-admin.jar"]
```



## 5. 启动服务

### 5.1 启动所有服务
```bash
# 进入opt/app根目录
cd /opt/app

# 启动服务
docker-compose up -d
```

## 6. SSL 证书配置

### 6.1 进入certd后台

```bash
# 网址输入（别忘记开放端口）
服务器IP:7001

默认账号密码：admin/123456
```

### 6.2 申请证书

[certd官方文档教程](https://gitee.com/certd/certd/blob/v2/step.md)

### 6.3 拿到cert.key和cert.pem 放到指定位置

[certd官方文档教程](https://gitee.com/certd/certd/blob/v2/step.md)

```bash
# 其中 主机-部署证书到SSH主机 这一步需要添加脚本命令
shell脚本命令： cd /opt/app && docker-compose up -d --build frontend

```

### 

- 
