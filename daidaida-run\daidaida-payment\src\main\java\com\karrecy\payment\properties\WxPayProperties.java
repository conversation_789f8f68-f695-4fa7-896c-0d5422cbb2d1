package com.karrecy.payment.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;
    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * apiV3key
     */
    private String apiV3Key;

    /**
     * 证书相对路径
     */
    private String keyPath;

    /**
     * 商户私钥路径
     */
    private String privateKeyPath;

    /**
     * 商户证书路径
     */
    private String privateCertPath;
    
    /**
     * 微信支付公钥
     */
    private String wechatPayPublicKey;
    
    /**
     * 微信支付公钥序列号
     */
    private String wechatPayPublicKeySerialNumber;
    
    /**
     * 商户API证书序列号
     * 对应RSAPublicKeyConfig.Builder().merchantSerialNumber()
     */
    private String merchantSerialNumber;

    /**
     * 下单回调地址
     */
    private String returnUrl;

    /**
     * 退款回调地址
     */
    private String refundUrl;

    /**
     * 是否启用降级策略
     * 在微信支付服务不可用时，是否使用降级方式继续提供服务
     * 仅用于开发测试环境
     */
    private Boolean enableFallback = false;
    
    /**
     * 是否使用公钥模式
     * 设置为true时，使用微信支付公钥进行验签，不下载平台证书
     * 设置为false时，使用平台证书模式
     */
    private Boolean usePublicKeyMode = true;

}
