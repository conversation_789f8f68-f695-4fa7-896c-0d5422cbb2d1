// @ts-nocheck
// This file is generated by <PERSON>i automatically
// DO NOT CHANGE IT MANUALLY!
import dayjs from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs';
import antdPlugin from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js';

import isSameOrBefore from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/isSameOrAfter';
import advancedFormat from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/advancedFormat';
import customParseFormat from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/customParseFormat';
import weekday from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/weekday';
import weekYear from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/weekYear';
import weekOfYear from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/weekOfYear';
import isMoment from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/isMoment';
import localeData from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/localeData';
import localizedFormat from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/localizedFormat';
import duration from 'E:/Projects/fuu-run/fuu-run-admin/fuu-run-admin/node_modules/dayjs/plugin/duration';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekYear);
dayjs.extend(weekOfYear);
dayjs.extend(isMoment);
dayjs.extend(localeData);
dayjs.extend(localizedFormat);
dayjs.extend(duration);

dayjs.extend(antdPlugin);
