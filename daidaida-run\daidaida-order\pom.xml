<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daidaida-run</artifactId>
        <groupId>com.karrecy</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daidaida-order</artifactId>

    <description>
        订单模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-system</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-payment</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>
</project>
