<template>
  <view class="container">
    <!-- 顶部卡片 -->
    <view class="top-card">
      <view class="user-info">
        <view class="credit-score">
          <text class="score">{{ userInfo.userWx.creditScore }}</text>
          <text class="label">信用分</text>
        </view>
      </view>
      
      <!-- 收益统计 -->
 <!--     <view class="earnings-row">
        <view class="earnings-item">
          <text class="amount">¥{{ todayEarnings }}</text>
          <text class="label">今日赏金</text>
        </view>
        <view class="earnings-item">
          <text class="amount">¥{{ totalEarnings }}</text>
          <text class="label">总赏金</text>
        </view>
      </view> -->
      
      <!-- 接单统计 -->
  <!--    <view class="orders-row">
        <view class="orders-item">
          <text class="number">{{ todayOrders }}</text>
          <text class="label">今日接单</text>
        </view>
        <view class="orders-item">
          <text class="number">{{ totalOrders }}</text>
          <text class="label">总接单量</text>
        </view>
      </view> -->
    </view>
    <view class="top-card" style="margin-top: 20px;">
    	  <view class="user-info">
    	    <view class="credit-score">
    	      <text style="font-size: large;" class="label">钱包</text>
    	    </view>
    	  </view>
    	  <!-- 收益统计 -->
    	    <view class="earnings-row">
    	      <view class="earnings-item">
    	        <text class="amount">¥{{ wallet.balance }}</text>
    	        <text class="label">余额</text>
    	      </view>
    	      <view class="earnings-item">
    	        <text class="amount">¥{{ wallet.withdrawn }}</text>
    	        <text class="label">已提现</text>
    	      </view>
    	    </view>
    	</view>
    <!-- 功能按钮 -->
    <view class="action-buttons">
      <button class="btn withdraw" @tap="handleWithdraw">提现</button>
      <button class="btn details" @tap="handleDetails">资金明细</button>
    </view>
  </view>
  
  
</template>

<script>
	import {getWallet} from '@/request/apis/payment.js'
	import {getInfo} from "@/request/apis/login.js"
export default {
  data() {
    return {
      creditScore: 100,
      todayEarnings: '0.00',
      totalEarnings: '0.00',
      todayOrders: 0,
      totalOrders: 0,
	  wallet:{},
	  userInfo:{
		  userWx:{
			  creditScore:0
		  }
	  }
    }
  },
  onLoad() {
	  this.getInfo()
  	this.getWalletInit()
  },
  methods: {
	  getWalletInit() {
		  getWallet().then(res => {
			  console.log(res);
			  this.wallet = res.data
		  })
		  
	  },
	  getInfo() {
	  	let that = this
	  	getInfo().then(res => {
	  		let info = res
	  		this.$store.commit('login', info.data.user);
	  		this.$store.commit('setConfig', info.data.config);
	  		this.userInfo = this.$store.state.userInfo;
	  	}).catch(err => {
	  		this.showDanger(err)
	  	})
	  },
    handleWithdraw() {
      // 处理提现逻辑
	  uni.navigateTo({
	  	url:"/pages/API/runner/center/recode/recode?balance="+this.wallet.balance
	  })
    },
    handleDetails() {
      // 处理查看资金明细逻辑
	  uni.navigateTo({
	  	url:"/pages/API/runner/center/capitalflow/capitalflow"
	  })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f7f9ff;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.top-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
  position: relative;
  overflow: hidden;
  
  .user-info {
    display: flex;
    justify-content: center;
    margin-bottom: 32rpx;
    position: relative;
    
    .credit-score {
      text-align: center;
      
      .score {
        font-size: 48rpx;
        font-weight: 600;
        color: #4F7DF5;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .label {
        font-size: 28rpx;
        color: #666;
        display: block;
      }
    }
  }
  
  .earnings-row {
    display: flex;
    justify-content: space-around;
    margin: 0 16rpx;
    position: relative;
    
    .earnings-item {
      text-align: center;
      padding: 24rpx 20rpx;
      background-color: #f8faff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
      flex: 1;
      margin: 0 16rpx;
      
      .amount {
        font-size: 36rpx;
        font-weight: 600;
        color: #ff6b6b;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.action-buttons {
  margin-top: 24rpx;
  display: flex;
  gap: 24rpx;
  padding: 0 16rpx;
  
  .btn {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.withdraw {
      background-color: #4F7DF5;
      color: #fff;
      border: none;
      box-shadow: 0 6rpx 16rpx rgba(79, 125, 245, 0.3);
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(79, 125, 245, 0.2);
      }
    }
    
    &.details {
      background-color: #fff;
      color: #333;
      border: 1px solid #ebedf0;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
      
      &:active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>