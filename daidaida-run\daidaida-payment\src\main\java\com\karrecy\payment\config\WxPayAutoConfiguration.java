package com.karrecy.payment.config;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.github.binarywang.wxpay.v3.auth.Verifier;
import com.karrecy.common.utils.StringUtils;
import com.karrecy.payment.properties.WxPayProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

/**
 * 微信支付相关配置
 */
@Configuration
@EnableConfigurationProperties(WxPayProperties.class)
@ConditionalOnClass(WxPayService.class)
@ConditionalOnProperty(prefix = "wx.pay", value = "enabled", matchIfMissing = true)
@Slf4j
public class WxPayAutoConfiguration {
    private WxPayProperties properties;
    private final ResourceLoader resourceLoader;

    @Autowired
    public WxPayAutoConfiguration(WxPayProperties properties, ResourceLoader resourceLoader) {
        this.properties = properties;
        this.resourceLoader = resourceLoader;
    }

    /**
     * 构造微信支付服务对象
     *
     * @return 微信支付service
     */
    @Bean
    @ConditionalOnMissingBean(WxPayService.class)
    public WxPayService wxPayService() {
        log.info("初始化微信支付服务");
        
        final WxPayServiceImpl wxPayService = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        
        // 设置基本配置
        payConfig.setAppId(StringUtils.trimToNull(this.properties.getAppId()));
        payConfig.setMchId(StringUtils.trimToNull(this.properties.getMchId()));
        payConfig.setApiV3Key(StringUtils.trimToNull(this.properties.getApiV3Key()));
        
        // 禁用沙箱环境
        payConfig.setUseSandboxEnv(false);
        
        // 检查是否配置了公钥模式
        boolean isPublicKeyModeConfigured = 
            StringUtils.isNotEmpty(properties.getWechatPayPublicKey()) && 
            StringUtils.isNotEmpty(properties.getWechatPayPublicKeySerialNumber()) &&
            StringUtils.isNotEmpty(properties.getMerchantSerialNumber()) &&
            StringUtils.isNotEmpty(properties.getPrivateKeyPath());
            
        // 检查是否配置了证书模式 (根据证书文件路径)
        boolean isCertificateModeConfigured = 
            StringUtils.isNotEmpty(properties.getKeyPath()) && 
            StringUtils.isNotEmpty(properties.getPrivateKeyPath()) && 
            StringUtils.isNotEmpty(properties.getPrivateCertPath());
        
        // 使用公钥模式 (优先)
        if (isPublicKeyModeConfigured && properties.getUsePublicKeyMode()) {
            log.info("使用微信支付公钥模式初始化");
            
            try {
                // 1. 加载商户私钥
                PrivateKey privateKey = loadPrivateKey(properties.getPrivateKeyPath());
                
                if (privateKey != null) {
                    // 2. 设置商户序列号
                    payConfig.setCertSerialNo(properties.getMerchantSerialNumber());
                    
                    // 3. 创建自定义验证器
                    CustomWxPayVerifier verifier = new CustomWxPayVerifier(
                        properties.getWechatPayPublicKeySerialNumber(), 
                        properties.getWechatPayPublicKey());
                    
                    // 4. 设置验证器和私钥
                    payConfig.setVerifier(verifier);
                    payConfig.setPrivateKey(privateKey);
                    
                    log.info("微信支付公钥模式配置完成，公钥序列号: {}, 商户API证书序列号: {}", 
                        properties.getWechatPayPublicKeySerialNumber(),
                        properties.getMerchantSerialNumber());
                } else {
                    log.error("无法加载商户私钥，请检查配置");
                }
            } catch (Exception e) {
                log.error("配置公钥模式失败: {}", e.getMessage(), e);
            }
        } 
        // 如果公钥模式未配置或未启用，尝试证书模式
        else if (isCertificateModeConfigured) {
            log.info("使用证书模式初始化");
            
            // 设置证书路径
            payConfig.setKeyPath(StringUtils.trimToNull(this.properties.getKeyPath()));
            payConfig.setPrivateKeyPath(StringUtils.trimToNull(this.properties.getPrivateKeyPath()));
            payConfig.setPrivateCertPath(StringUtils.trimToNull(this.properties.getPrivateCertPath()));
            
            if (StringUtils.isNotEmpty(properties.getMerchantSerialNumber())) {
                payConfig.setCertSerialNo(properties.getMerchantSerialNumber());
                log.info("设置商户API证书序列号: {}", properties.getMerchantSerialNumber());
            }
        } else {
            log.warn("未配置公钥模式或证书模式，微信支付API可能无法正常工作");
            log.warn("请配置以下参数之一：");
            log.warn("1. 公钥模式：wechatPayPublicKey、wechatPayPublicKeySerialNumber、merchantSerialNumber、privateKeyPath");
            log.warn("2. 证书模式：keyPath、privateKeyPath、privateCertPath");
        }
        
        // 设置到支付服务
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }
    
    /**
     * 从路径加载私钥
     */
    private PrivateKey loadPrivateKey(String privateKeyPath) {
        try {
            Resource resource = resourceLoader.getResource(privateKeyPath);
            InputStream inputStream = resource.getInputStream();
            String privateKeyPEM = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            
            // 移除PEM格式头尾和换行符
            privateKeyPEM = privateKeyPEM
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
            
            // 解码BASE64得到DER格式私钥
            byte[] privateKeyDER = Base64.getDecoder().decode(privateKeyPEM);
            
            // 加载PKCS8格式私钥
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyDER);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("加载商户私钥失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 自定义验证器，直接使用配置的公钥而不是从平台获取证书
     */
    public static class CustomWxPayVerifier implements Verifier {
        private final String serialNumber;
        private final String publicKeyContent;
        
        public CustomWxPayVerifier(String serialNumber, String publicKeyContent) {
            this.serialNumber = serialNumber;
            this.publicKeyContent = publicKeyContent;
            log.info("创建自定义验证器，微信支付公钥序列号: {}", serialNumber);
        }
        
        @Override
        public boolean verify(String serialNumber, byte[] message, String signature) {
            if (!this.serialNumber.equals(serialNumber)) {
                log.warn("验证失败：序列号不匹配，期望: {}, 实际: {}", this.serialNumber, serialNumber);
                return false;
            }
            
            try {
                // 使用JDK的API加载公钥并验证签名
                java.security.PublicKey publicKey = loadPublicKey(publicKeyContent);
                if (publicKey == null) {
                    log.error("验证失败：无法加载微信支付公钥");
                    return false;
                }
                
                java.security.Signature sign = java.security.Signature.getInstance("SHA256withRSA");
                sign.initVerify(publicKey);
                sign.update(message);
                boolean result = sign.verify(java.util.Base64.getDecoder().decode(signature));
                
                if (!result) {
                    log.warn("验证失败：签名不匹配");
                }
                
                return result;
            } catch (Exception e) {
                log.error("验证过程中发生异常: {}", e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 手动实现公钥加载逻辑
         */
        private java.security.PublicKey loadPublicKey(String publicKeyString) {
            try {
                // 移除PEM头尾和换行符
                String publicKeyPEM = publicKeyString
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s", "");
                
                // 解码base64得到DER格式的密钥
                byte[] keyBytes = java.util.Base64.getDecoder().decode(publicKeyPEM);
                
                // 加载RSA公钥
                java.security.spec.X509EncodedKeySpec spec = new java.security.spec.X509EncodedKeySpec(keyBytes);
                java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance("RSA");
                return keyFactory.generatePublic(spec);
            } catch (Exception e) {
                log.error("加载微信支付公钥失败: {}", e.getMessage(), e);
                return null;
            }
        }

        @Override
        public X509Certificate getValidCertificate() {
            // 使用公钥模式不需要证书
            return null;
        }
    }
}
