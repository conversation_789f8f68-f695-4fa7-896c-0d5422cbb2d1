// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as addressController from './addressController';
import * as amapController from './amapController';
import * as delayedQueueController from './delayedQueueController';
import * as loginController from './loginController';
import * as moneyController from './moneyController';
import * as orderAppealController from './orderAppealController';
import * as orderChatController from './orderChatController';
import * as orderController from './orderController';
import * as ossConfigController from './ossConfigController';
import * as ossController from './ossController';
import * as payNotifyController from './payNotifyController';
import * as profileController from './profileController';
import * as regionController from './regionController';
import * as runnerApplyController from './runnerApplyController';
import * as schoolController from './schoolController';
import * as systemController from './systemController';
import * as tagController from './tagController';
import * as userPcController from './userPcController';
import * as userWxController from './userWxController';
export default {
  userPcController,
  runnerApplyController,
  profileController,
  ossConfigController,
  moneyController,
  tagController,
  orderAppealController,
  schoolController,
  regionController,
  addressController,
  loginController,
  systemController,
  ossController,
  orderController,
  payNotifyController,
  userWxController,
  orderChatController,
  delayedQueueController,
  amapController,
};
