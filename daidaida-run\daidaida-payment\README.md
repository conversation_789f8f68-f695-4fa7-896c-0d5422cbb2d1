# 微信支付集成指南

## 微信支付公钥模式配置

### 背景介绍
微信支付提供了两种验证模式：平台证书模式和公钥模式。公钥模式是推荐的方式，更加简单和安全。

### 配置步骤

#### 1. 获取必要的证书和参数

1. **商户API证书**：
   - 登录[微信商户平台](https://pay.weixin.qq.com) -> 账户中心 -> API安全 -> 申请API证书
   - 下载证书后，将私钥文件(apiclient_key.pem)保存到安全位置

2. **商户API证书序列号**：
   - 在微信商户平台 -> 账户中心 -> API安全 -> 设置页面可以查看证书序列号
   - 复制"证书序列号"字段的值

3. **微信支付公钥**：
   - 下载微信支付平台公钥：微信商户平台 -> 账户中心 -> API安全 -> 公钥管理 -> 下载平台证书
   - 保存文件，文件内容即为微信支付公钥

4. **微信支付公钥序列号**：
   - 在微信商户平台 -> 账户中心 -> API安全 -> 公钥管理页面可以查看公钥序列号
   - 复制"序列号"字段的值

5. **API v3密钥**：
   - 在微信商户平台 -> 账户中心 -> API安全 -> 设置页面可以查看或设置API v3密钥

#### 2. 配置application.yml

在`application.yml`或相关环境的配置文件中添加以下配置：

```yaml
wx:
  pay:
    app-id: xxxxxxxxxxxxxxxx  # 小程序或公众号的appId
    mch-id: xxxxxxxxxxxxxxxx  # 微信支付商户号
    api-v3-key: xxxxxxxxxxxxxxxxxxxxxxxx  # API v3密钥
    
    # 公钥模式配置（推荐）
    use-public-key-mode: true  # 启用公钥模式
    private-key-path: /path/to/apiclient_key.pem  # 商户API证书私钥路径
    # 或者直接配置私钥内容
    # private-key-content: |
    #   -----BEGIN PRIVATE KEY-----
    #   私钥内容...
    #   -----END PRIVATE KEY-----
    merchant-serial-number: xxxxxxxxxxxxxxxx  # 商户API证书序列号
    wechat-pay-public-key: |
      -----BEGIN PUBLIC KEY-----
      微信支付公钥内容...
      -----END PUBLIC KEY-----
    wechat-pay-public-key-serial-number: xxxxxxxxxxxxxxxx  # 微信支付公钥序列号
    
    # 回调地址
    return-url: https://example.com/api/payment/notify/pay
    refund-url: https://example.com/api/payment/notify/refund
    
    # 开发环境配置
    enable-fallback: false  # 测试环境可设置为true，生产环境必须为false
    
    # 以下为旧版配置，保留以确保兼容性
    mch-key: xxxxxxxxxxxxxxxx  # API v2密钥
    key-path: /path/to/apiclient_cert.p12  # v2版证书路径
    private-cert-path: /path/to/apiclient_cert.pem  # v3版证书路径
```

#### 3. 常见问题

1. **无可用的平台证书**：
   - 确保已正确配置公钥模式
   - 检查`use-public-key-mode`是否设置为`true`
   - 确认`wechat-pay-public-key`和`wechat-pay-public-key-serial-number`配置正确

2. **请确保证书文件地址或者内容已配置**：
   - 确认`private-key-path`指向的文件存在且有读取权限
   - 或者直接配置`private-key-content`
   - 使用绝对路径可减少路径问题

3. **验证签名失败**：
   - 确保`wechat-pay-public-key`格式正确，包含头尾部分
   - 检查`merchant-serial-number`和`wechat-pay-public-key-serial-number`是否对应

4. **降级模式**：
   - 开发环境可以设置`enable-fallback: true`，支付将使用模拟数据
   - 生产环境必须设置为`false`

### 证书路径说明

系统支持多种路径格式：

- **绝对路径**：`/path/to/cert.pem`
- **相对路径**：`cert.pem`（相对于应用运行目录）
- **类路径**：`classpath:cert/cert.pem`（从resources目录加载）
- **文件URL**：`file:/path/to/cert.pem`

建议使用绝对路径或类路径以避免路径解析问题。 