import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import { base_url, upload_url } from './request/request.js'

Vue.config.productionTip = false
import store from './store'   // 引入 vuex

// 挂载vuex
Vue.prototype.$store = store;

// 挂载请求基础URL和上传URL
Vue.prototype.$baseUrl = base_url;
Vue.prototype.$uploadUrl = upload_url;

App.mpType = 'app'
const app = new Vue({
  ...App,
  store,  // 将 store 传入 Vue 实例

})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import store from './store'   // 引入 vuex

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)  // 使用 Vuex store 
  return { app }
}
// #endif
