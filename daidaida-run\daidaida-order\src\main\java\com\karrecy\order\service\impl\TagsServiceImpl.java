package com.karrecy.order.service.impl;

import com.karrecy.order.domain.po.Tags;
import com.karrecy.order.mapper.TagsMapper;
import com.karrecy.order.service.ITagsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * tag表 服务实现类
 * </p>
 */
@Service
@Slf4j
public class TagsServiceImpl extends ServiceImpl<TagsMapper, Tags> implements ITagsService {

    @Override
    public Tags getTagByNameAndSchool(String tagName, Long schoolId, Integer serviceType) {
        // 创建查询条件
        LambdaQueryWrapper<Tags> queryWrapper = new LambdaQueryWrapper<>();
        // 按标签名称查询
        queryWrapper.eq(Tags::getName, tagName);
        // 按学校ID查询
        queryWrapper.eq(Tags::getSchoolId, schoolId);
        // 按服务类型查询
        queryWrapper.eq(Tags::getServiceType, serviceType);
        
        // 记录查询条件
        log.info("查询标签，标签名称: {}, 学校ID: {}, 服务类型: {}", tagName, schoolId, serviceType);
        
        // 执行查询并返回结果
        Tags tag = this.getOne(queryWrapper);
        if (tag != null) {
            log.info("找到标签: {}, 金额: {}", tagName, tag.getAmount());
        } else {
            log.warn("未找到标签: {}, 学校ID: {}, 服务类型: {}", tagName, schoolId, serviceType);
        }
        return tag;
    }
    
    @Override
    public BigDecimal calculateMultiTagsAmount(String combinedTagNames, Long schoolId, Integer serviceType) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // 检查传入的标签名称是否为空
        if (combinedTagNames == null || combinedTagNames.trim().isEmpty()) {
            log.warn("传入的标签名称为空");
            return totalAmount;
        }
        
        log.info("开始计算多标签金额，标签组合: {}, 学校ID: {}, 服务类型: {}", combinedTagNames, schoolId, serviceType);
        
        // 按逗号分隔标签
        String[] tagNames = combinedTagNames.split("，|,");
        log.info("拆分标签名称: {}", String.join(", ", tagNames));
        
        // 查询每个标签并累加金额
        for (String tagName : tagNames) {
            // 提取标签名称和数量
            String pureName = tagName;
            int quantity = 1;
            
            // 检查标签是否包含数量信息（如"小件×2"格式）
            if (tagName.contains("×")) {
                String[] parts = tagName.split("×");
                pureName = parts[0].trim();
                try {
                    quantity = Integer.parseInt(parts[1].trim());
                } catch (NumberFormatException e) {
                    log.warn("无法解析标签数量: {}", tagName);
                }
            }
            
            log.info("处理标签: {}, 净标签名: {}, 数量: {}", tagName, pureName, quantity);
            
            // 查询标签
            Tags tag = getTagByNameAndSchool(pureName, schoolId, serviceType);
            
            // 如果找到标签并且金额不为空，加到总金额中
            if (tag != null && tag.getAmount() != null) {
                BigDecimal tagAmount = new BigDecimal(tag.getAmount().toString());
                BigDecimal quantityDecimal = new BigDecimal(quantity);
                totalAmount = totalAmount.add(tagAmount.multiply(quantityDecimal));
                log.info("找到标签: {}, 金额: {}, 数量: {}, 小计: {}", 
                         pureName, tagAmount, quantity, tagAmount.multiply(quantityDecimal));
            } else {
                log.warn("未找到标签或标签金额为空: {}, 服务类型: {}", pureName, serviceType);
            }
        }
        
        log.info("多标签总金额计算完成: {}, 服务类型: {}", totalAmount, serviceType);
        return totalAmount;
    }
}
