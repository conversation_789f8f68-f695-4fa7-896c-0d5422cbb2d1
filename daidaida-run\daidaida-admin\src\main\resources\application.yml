# 项目相关配置
daidaida:
  # 名称
  name: daidaida
  # 版本
  version: 1.0.1
  # 版权年份
  copyrightYear: 2030
  # 超时未支付取消时长 (分钟)
  pay-cancel-ttl: 15
  # 超时未完成自动完成时长 (小时)
  auto-complete-ttl: 24
  # 完成订单凭证上限 （张）
  completion-images-limit: 5
  # 信用分上限（初始）
  credit-upper_limit: 12
  # 信用分下限
  credit-lower-limit: 2
  # 信用分每次扣除
  credit-deduction: 1
  # 用户地址上限
  max-address: 20
email:
  host: smtp.163.com
  port: 25
  user: <EMAIL>
  pass: XJhre3A33cNPYgge

chat:
  websocket:
    port: 4400
    path: /ws
    boss: 2
    work: 20


# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
      write-dates-as-timestamps: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false


--- # 数据源配置
spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
    url: **************************************************************************************************************************************************************************************************
    username: root
    password: root

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: com.karrecy.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.karrecy.**.domain
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl  org.apache.ibatis.logging.nologging.NoLoggingImpl
    logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      idType: ASSIGN_ID
      # 逻辑已删除值
      logicDeleteValue: 2
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL
      insertStrategy: NOT_NULL
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_NULL
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      where-strategy: NOT_NULL

springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true

# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 9
    # 密码(如没有密码请注释掉)
    password: 123456
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes:
  # 匹配链接
  urlPatterns: /**

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期 设为一天 (必定过期) 单位: 秒
  timeout: 86400
  # 多端不同 token 有效期 可查看 LoginHelper.loginByDevice 方法自定义
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: 1800
  # 允许动态设置 token 有效期
  dynamic-active-timeout: true
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiSm9obiIsImFkbWluIjp0cnVlfQ.OI5ht9P98GhFNixYpJHkwDM30NQDTcBE_XqDtmZCHn_QR1IA91S6X0A7UFCjog6Le6VlpKVXoeuS8GGbC-wYsQ
  auto-renew: true

wx:
  miniapp:
    configs:
      - appid: wx6e2eef6acc7265fa
        secret: cdf3ffbde8f891a608a490803fe01c68
        token: 92_DZdz_Ta_8330yHgFyk376SJWk-PrOgMuUaFNWGRDFCsxik3RiNGQ_QNxxj-_BdS8zgYe3uHEV-a5shrBkaHYEuhY9GTY9PdN1B7ddc-TLuYKOwFnTjQjUB4CxXEEGIfAHAYAC
        aesKey: f0m3oMnhm9D5nujDfSVDO710uLsse5nA6n4xy2QuLzy
        msgDataFormat: JSON

  pay:
    # 小程序或公众号AppID (需要替换为实际值)
    app-id: wx6e2eef6acc7265fa
    # 商户号 (需要替换为实际值)
    mch-id: 1716808176
    # 商户APIv3密钥 (需要替换为实际值)
    api-v3-key: daidaidapaotuigongzuoshi99999999
    # 商户API证书序列号 (需要替换为实际值)
    merchant-serial-number: 2D0B3C8B8CF88CBC8BB8F223B764FF270D190E67
    # 商户私钥路径
    private-key-content: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    # 微信支付公钥 (需要替换为实际值，包括BEGIN和END行)
    wechat-pay-public-key: |
      -----BEGIN PUBLIC KEY-----
        MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk422l9a172E3RdgOQaJ8
        v/dcbTVYo8++7lyKXxNnmB5CjY2Am1SuW51N1MJ7YqPLz/LhYeRfqjQYzQFo9SZR
        2YA8Bs8MrXrNszlSvj+uwR6oNB+RPE66vOIZCwnL0RZ81FOt1pbA8zmQZg36cz+L
        6rqgbWr4+FBSetz845v6NZimjl2StjJ3cmlEoh8fvskWg35rO8pPgCidBx0Kn3C8
        w1kkHPhqhn7ObTymcpvFhJ4rn2i2k+aRyENcpssRUbbwfOUiAod2+12X3M/u0y28
        lcrNLXKqQsLyr2w8ZaN090zD79l17h8x9KIa6g6KcH7qqLYWI5R3wk5TBvCcSaVe
        RQIDAQAB
      -----END PUBLIC KEY-----
    # 微信支付公钥序列号 (需要替换为实际值，格式如PUB_KEY_ID_XXXXXX)
    wechat-pay-public-key-serial-number: PUB_KEY_ID_0117168081762025051200451919002001
    # 回调地址
    return-url: https://localhost/dev/notify/payNotify
    # 退款回调地址
    refund-url: https://localhost/dev/notify/refundNotify
    # 是否使用公钥模式 (推荐true)
    use-public-key-mode: true
    # SDK选择开关
    use-official-sdk: true  # 使用官方SDK，设为false则使用旧版SDK
    # 是否启用降级策略 (开发环境建议开启)
    enable-fallback: false
    
    # 以下是传统平台证书模式的配置，使用公钥模式时可以不设置
    # key-path: classpath:cert/apiclient_cert.p12
    # private-cert-path: classpath:cert/apiclient_cert.pem



