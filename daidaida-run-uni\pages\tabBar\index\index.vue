<template>
	 <nut-navbar  fixed="true" placeholder="true" class="custom-navbar">
	    <template #left>
			<div @click="goToSelectSchool" class="school-selector">
			  <span class="school-name">{{ currSchool.name }}</span>
			  <nut-icon name="triangle-down" size="12"/>
			</div>
	    </template>
	  </nut-navbar>
	  
	  <view class="container">
		  <nut-swiper class="custom-swiper" :init-page="page" :pagination-visible="true" pagination-color="#4F7DF5" auto-play="3000">
		   <nut-swiper-item v-for="item in list" :key="item">
				  <img :src="item" alt="" class="swiper-image" />
			</nut-swiper-item>
		</nut-swiper>
		
		  <view class="service-grid">
			<nut-grid :gutter="16" :column-num="2" direction="horizontal">
				<nut-grid-item @click="toOrderQusong" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/qu.jpg" />
						<text class="grid-text">帮取</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderQusong" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/song.jpg" />
						<text class="grid-text">帮送</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderBangmai" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/mai.jpg" />
						<text class="grid-text">帮买</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderWanneg" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/wan.jpg" />
						<text class="grid-text">万能</text>
					</view>
				</nut-grid-item>
	  </nut-grid>
		  </view>
	  </view>

  <nut-toast></nut-toast>
   <nut-notify></nut-notify>
  
</template>
<script>
	import { getCarousel } from "@/request/apis/login.js"
	import { toRaw } from "vue";
	export default {
		data() {
			return {
				list:[
            'https://pic1.zhimg.com/80/v2-f24ff4c534347c07ee0d528e0fe33f9d.jpg',
            'https://gss0.baidu.com/9vo3dSag_xI4khGko9WTAnF6hhy/zhidao/pic/item/a8014c086e061d95b3645b8d75f40ad162d9ca61.jpg',
          ],
				title: 'Hello',
				currSchool: {
					name:'选择校区'
				},
				orderServers:[]
			}
		},
		watch:{
			"$store.state.appLaunch": function(val, oldval) {
					console.log(val,oldval);
					if(val) {
						this.initSchool()
					}
			}
		},
		mounted() {
			console.log("mounted");
			this.initSchool()
			
		},
		onLoad() {
			console.log("index onLoad");
			getCarousel().then(res => {
				this.list = res.data
			})
			const checkOperationStatus = setInterval(() => {
			  if (this.$store.state.appLaunch) {
				  this.initSchool()
				clearInterval(checkOperationStatus);
				console.log('首页的js文件中的代码执行');
			  }
			}, 100); // 每100毫秒检查一次状态变化
			
		},
		onShow() {
			
		},
		methods: {
			toLogin() {
				uni.navigateTo({
					url:'/pages/API/login/login'
				})
			},
			toChat() {
				
				uni.navigateTo({
					url:"/pages/API/chat/chat"
				})
			},
			toDetail() {
				uni.navigateTo({
					url:"/pages/API/order/detail/detail"
				})
			},
			test() {
				const SUBSCRIBE_ID = 'nFzHoJjaKP8W6jdiFZkXsX6Z2A1u6O1F7wGrNAUpBlY' // 模板ID
				let that = this;
				    if (wx.requestSubscribeMessage) {
				      wx.requestSubscribeMessage({
				        tmplIds: [SUBSCRIBE_ID],
				        success(res) {
				          if (res[SUBSCRIBE_ID] === 'accept') {
				            // 用户主动点击同意...do something
				          } else if (res[SUBSCRIBE_ID] === 'reject') {
				            // 用户主动点击拒绝...do something
				          } else {
				            wx.showToast({
				              title: '授权订阅消息有误',
				              icon: 'none'
				            })
				          }
				        },
				        fail(res) {
				          // 20004:用户关闭了主开关，无法进行订阅,引导开启
				          if (res.errCode == 20004) {
				          	// 显示引导设置弹窗
				            that.setData({
				              isShowSetModel: true
				            })
				          }else{
				          	// 其他错误信息码，对应文档找出原因
				            wx.showModal({
				              title: '提示',
				              content: res.errMsg,
				              showCancel: false
				            })
				          }
				        }
				      });
				    } else {
				      wx.showModal({
				        title: '提示',
				        content: '请更新您微信版本，来获取订阅消息功能',
				        showCancel: false
				      })
				    }
			},
			toOrderQusong() {
				uni.navigateTo({
					url:"/pages/API/order/qusong/qusong"
				})
			},
			toOrderBangmai() {
				uni.navigateTo({
					url:"/pages/API/order/bangmai/bangmai"
				})
			},
			toOrderWanneg() {
				uni.navigateTo({
					url:"/pages/API/order/wanneng/wanneng"
				})
			},
			toOrderSubmit(){
				// uni.navigateTo({
				// 	url:"/pages/API/order/qusong/qusong"
				// })
				// uni.navigateTo({
				// 	url:"/pages/API/order/bangmai/bangmai"
				// })
				uni.navigateTo({
					url:"/pages/API/order/wanneng/wanneng"
				})
				// uni.navigateTo({
				// 	url:"/pages/API/order/test/test"
				// })
			},
			initSchool(){
				let currSchool = this.$store.state.currSchool;
				console.log(currSchool);
				if(currSchool == null) {
					
				}
				else {
					this.loadSchoolByStore();
				}
			},
			loadSchoolByStore(){
				this.currSchool = toRaw(this.$store.state.currSchool);
				console.log(this.currSchool);
			},
			goToSelectSchool(){
				uni.navigateTo({
					url:"/pages/API/school/select/select"
				})
			}
		},
		
	}
</script>
<style lang="scss" scoped>
// 页面背景色
page {
  background-color: #f7f9ff;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  padding: 0 16rpx 32rpx;
}

// 自定义导航栏
.custom-navbar {
  :deep(.nut-navbar__title) {
    color: #4F7DF5;
    font-weight: 600;
  }
  
  :deep(.nut-navbar__left) {
    padding-left: 24rpx;
  }
}

// 学校选择器
.school-selector {
  display: flex;
  align-items: center;
  background-color: rgba(79, 125, 245, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  
  .school-name {
    max-width: 160rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 28rpx;
    font-weight: 500;
    color: #4F7DF5;
    margin-right: 8rpx;
}

  :deep(.nut-icon) {
    color: #4F7DF5;
  }
}

// 轮播图样式
.custom-swiper {
  margin: 32rpx 16rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  
  .swiper-image {
    width: 100%;
    height: 320rpx;
    border-radius: 24rpx;
    object-fit: cover;
  }
  
  :deep(.nut-swiper-pagination-bullet) {
    width: 16rpx;
    height: 16rpx;
    opacity: 0.6;
  background: #fff;
    border-radius: 8rpx;
    transition: all 0.3s;
}

  :deep(.nut-swiper-pagination-bullet-active) {
    width: 32rpx;
    opacity: 1;
    background: #4F7DF5;
  }
}

// 服务网格
.service-grid {
  margin-top: 48rpx;
  
  :deep(.nut-grid) {
    padding: 16rpx 0;
  }
  
  .grid-item {
    padding: 12rpx;
  }
  
  .grid-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
      transition: all 0.3s ease;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
      
      &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }
      
    .gridIcon {
      width: 120rpx !important;
      height: 120rpx !important;
      margin-bottom: 16rpx;
      border-radius: 20rpx;
    }
    
    .grid-text {
      font-size: 32rpx;
      font-weight: 600;
        color: #333;
      margin-top: 16rpx;
    }
  }
}
</style>