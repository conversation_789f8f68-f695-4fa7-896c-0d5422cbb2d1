package com.karrecy.admin.controller.pay;



import cn.dev33.satoken.annotation.SaIgnore;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.payment.service.IPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付回调相关接口
 */
@RestController
@RequestMapping("/notify")
@Slf4j
@RequiredArgsConstructor
public class PayNotifyController {

    private final IPayService payService;

    /**
     * 支付成功回调
     */
    @SaIgnore
    @PostMapping("/payNotify")
    public String paySuccessNotify(@RequestBody String notifyData, HttpServletRequest request) {
        return payService.payNotifyV3(notifyData, request);

    }

    /**
     * 退款成功回调
     */
    @SaIgnore
    @PostMapping("/refundNotify")
    public String refundNotify(@RequestBody String notifyData, HttpServletRequest request) {
        return payService.refundNotify(notifyData, request);

    }

    /**
     * 模拟支付成功
     * 用于测试环境下，手动触发支付成功流程
     */
    @SaIgnore
    @GetMapping("/mockPaySuccess/{orderId}")
    public R<Void> mockPaySuccess(@PathVariable String orderId) {
        log.info("模拟支付成功，订单ID: {}", orderId);
        // 将订单ID添加到支付成功队列中
        QueueUtils.addQueueObject(QueueNames.ORDER_PAY_SUCCESS, orderId);
        return R.ok();
    }

}
