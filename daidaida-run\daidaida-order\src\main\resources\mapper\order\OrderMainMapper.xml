<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.karrecy.order.mapper.OrderMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.karrecy.order.domain.po.OrderMain">
        <id column="id" property="id" />
        <result column="school_id" property="schoolId" />
        <result column="service_type" property="serviceType" />
        <result column="tag" property="tag" />
        <result column="weight" property="weight" />
        <result column="start_address" property="startAddress" typeHandler="com.karrecy.common.handler.JsonTypeHandler"/>
        <result column="end_address" property="endAddress" typeHandler="com.karrecy.common.handler.JsonTypeHandler"/>
        <result column="detail" property="detail" />
        <result column="is_timed" property="isTimed" />
        <result column="specified_time" property="specifiedTime" />
        <result column="auto_cancel_ttl" property="autoCancelTtl" />
        <result column="gender" property="gender" />
        <result column="estimated_price" property="estimatedPrice" />
        <result column="total_amount" property="totalAmount" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="user_id" property="userId" />
        <result column="runner_id" property="runnerId" />
    </resultMap>

</mapper>
