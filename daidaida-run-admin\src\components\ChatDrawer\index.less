.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .messageList {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .messageItem {
      display: flex;
      margin-bottom: 16px;
      gap: 8px;
      
      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        flex-shrink: 0;
        transition: opacity 0.2s;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
        
        &:active {
          opacity: 0.7;
        }
      }
      
      .contentBox {
        max-width: 70%;
        
        .contentTop {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
          font-size: 12px;
          color: #999;
          
          &.textRight {
            justify-content: flex-end;
          }
          
          .ellipsis {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .content {
          padding: 8px 12px;
          background-color: #f0f0f0;
          border-radius: 0 8px 8px 8px;
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .chatTime {
            font-size: 12px;
            color: #999;
            align-self: flex-start;
          }
        }
      }
      
      &.self {
        justify-content: end;
        
        .contentBox {
          .contentTop {
            justify-content: flex-end;
          }
          
          .content {
            background-color: #1890ff;
            color: white;
            border-radius: 8px 0 8px 8px;
            
            .chatTime {
              align-self: flex-end;
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
        
        .avatar {
        }
      }
    }
  }
  
  .inputArea {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
    
    .ant-input-textarea {
      margin-bottom: 8px;
    }
    
    button {
      width: 100%;
    }
  }
} 