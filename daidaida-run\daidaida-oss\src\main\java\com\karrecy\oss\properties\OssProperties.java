package com.karrecy.oss.properties;

import lombok.Data;
import java.util.Objects;

/**
 * OSS对象存储 配置属性
 */
@Data
public class OssProperties {

    /**
     * 访问站点
     */
    private String endpoint;

    /**
     * 自定义域名
     */
    private String domain;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * ACCESS_KEY
     */
    private String accessKey;

    /**
     * SECRET_KEY
     */
    private String secretKey;

    /**
     * 存储空间名
     */
    private String bucketName;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 是否https（Y=是,N=否）
     */
    private String isHttps;

    /**
     * 桶权限类型(0private 1public 2custom)
     */
    private String accessPolicy;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OssProperties that = (OssProperties) o;
        return Objects.equals(endpoint, that.endpoint) &&
                Objects.equals(domain, that.domain) &&
                Objects.equals(prefix, that.prefix) &&
                Objects.equals(accessKey, that.accessKey) &&
                Objects.equals(secretKey, that.secretKey) &&
                Objects.equals(bucketName, that.bucketName) &&
                Objects.equals(region, that.region) &&
                Objects.equals(isHttps, that.isHttps) &&
                Objects.equals(accessPolicy, that.accessPolicy);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endpoint, domain, prefix, accessKey, secretKey, bucketName, region, isHttps, accessPolicy);
    }
}
