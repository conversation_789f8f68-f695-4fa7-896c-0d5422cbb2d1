<template>
	<template>
	  <nut-notify></nut-notify>
	</template>
	<scroll-view scroll-y style="height: 100vh;">
		<div class="box">
				<nut-cell @click="toAddAddress('start')" :title="'驿站  '+submitForm.startAddress.title+' '+submitForm.startAddress.detail" :sub-title="submitForm.startAddress.name+' '+submitForm.startAddress.phone">
					<template #icon>
							<image class="nut-icon" src="/static/icons/start.png"></image>
					</template>
					<template #link>
						<nut-button @tap.stop="showStartAddresses" size="mini" type="primary">地址簿</nut-button>
					</template>
				</nut-cell>
				<nut-cell @click="toAddAddress('end')" :title="'收货地址  '+submitForm.endAddress.title+' '+submitForm.endAddress.detail" :sub-title="submitForm.endAddress.name+' '+submitForm.endAddress.phone">
					<template #icon>
						<image class="nut-icon" src="/static/icons/end.png"></image>
					</template>
					<template #link>
						<nut-button @tap.stop="showEndAddresses($event)" size="mini" type="primary">地址簿</nut-button>
					</template>
				</nut-cell>
				<nut-cell @click="showDatePopupClick" is-link title="配送时间" :desc="specifiedTimeText">
					<!-- <template #icon>
						<image class="nut-icon" src="/static/logo.png"></image>
					</template> -->
				</nut-cell>
				<nut-cell @click="showGenderPopup = true" is-link title="性别限制" :desc="genderText">
					<!-- <template #icon>
						<image class="nut-icon" src="/static/logo.png"></image>
					</template> -->
				</nut-cell>
			</div>
			<div class="box">
				<nut-cell @click="showServiceTypePopup = true" is-link title="物品类型" :desc="submitForm.tag == '' ? '' : submitForm.tag+'-'+submitForm.weight">
					<template #icon>
						<image class="nut-icon" src="/static/icons/包裹.png"></image>
					</template>
				</nut-cell>
				<div style="position: relative;">
					<nut-textarea placeholder="请输入详细描述" v-model="submitForm.detail" limit-show max-length="100" />
					<div @click="showImagePopup = true" class="myImage">
						<nut-icon name="/static/icons/添加图片.png"></nut-icon>
						<span style="height: 100%;vertical-align: middle;">补充物品图</span>
					</div>
				</div>
		
			</div>
			<div class="box">
		
				<nut-cell @click="showMoneyPopup = true" is-link :title="submitForm.isUrgent ? '加急订单 - 追加金额' : '普通订单'" :desc="submitForm.isUrgent ? submitForm.additionalAmount + '元' : ''">
					<template #icon>
						<image class="nut-icon" src="/static/icons/钱币.png"></image>
					</template>
				</nut-cell>
		<!-- 		<nut-cell is-link title="商品预估金额" desc="">
		
				</nut-cell> -->
		
			</div>
			<div class="box">
				<nut-cell @click="showCancelTimePopup = true" is-link title="自动取消" :desc="autoCancelText">
		
				</nut-cell>
				<nut-cell title="支付方式" desc="微信支付">
					<template #link>
						<image style="margin-left: 5px;" class="nut-icon" src="/static/icons/微信支付.png"></image>
					</template>
				</nut-cell>
			</div>
			
			<div class="orderBtnBox">
				<view class="boxLeft">
					<span class="boxPrice">{{totalPrice}}</span>
					<span class="boxPrice" style="font-size: small;"> 元</span>
				</view>
				<div class="boxRight">
					<button :loading="btnSubmitLoading" type="primary" class="myBtn" @click="submitOrder">提交订单</button>
				</div>
			</div>
			
			<div class="box" style="height: 15%;background-color: transparent;box-shadow: none;">
				<div style="width: 100%;height: 20%;">
				</div>
			</div>
				
		
			<template>
				<nut-popup v-model:visible="showGenderPopup" position="bottom" safe-area-inset-bottom>
					<nut-picker :columns="genderData" title="性别限制" 
						@cancel="showGenderPopup = false" @confirm="onGenderConfirm">
					</nut-picker>
				</nut-popup>
				<nut-popup v-model:visible="showDatePopup" position="bottom" safe-area-inset-bottom>
					<nut-date-picker v-model="currentDate" :min-date="minDate" :max-date="maxDate" type="datetime"
						:minute-step="15" is-show-chinese @confirm="onTimedConfirm" ok-text="指定时间"
						@cancel="onTimedCancel" cancel-text="尽快配送">
					</nut-date-picker>
				</nut-popup>
				<nut-popup v-model:visible="showServiceTypePopup" position="bottom" safe-area-inset-bottom>
					<div class="nut-picker__bar">
						<div class="nut-picker__cancel nut-picker__left nut-picker__button">取消</div>
						<div class="nut-picker__title">物品类型</div>
						<div @click="confirmTag" class="nut-picker__confirm nut-picker__right nut-picker__button">确认</div>
					</div>
					<div class="custom-content">
						<nut-tag 
							v-for="(item, index) in tagList"
							:key="index" 
							custom-color="#fa2400" 
							@click="toggleTag(index)"
							:plain="!selectedTags.includes(index)">{{item.name}} {{item.price ? '+¥'+item.price : ''}}</nut-tag>
						
						<div v-show="tagRemark != ''" class="remark">{{tagRemark}}</div>
						<div v-show="selectedTags.includes(tagList.length-1)">
							<nut-input v-model="submitForm.tag" placeholder="其它" max-length="8" show-word-limit></nut-input>
						</div>
						
						<div v-for="tagIndex in selectedTags" :key="'qty-'+tagIndex" v-if="tagIndex !== tagList.length-1" class="quantity-selector">
							<div class="tag-name">{{tagList[tagIndex].name}} 数量：</div>
							<nut-input-number v-model="tagQuantities[tagIndex]" min="1" @change="onQuantityChange"></nut-input-number>
						</div>
						
						<div class="range-box">
							<div style="margin-bottom: 26px;" class="remark">
								<span style="font-weight: bold;color: black;">物品重量: </span>{{submitForm.weight}} (按实际情况填写)
							</div>
							<nut-range v-model="rangeValue" :max="20" :min="2" @change="onWeightChange" />
						</div>
					</div>
		
		
				</nut-popup>
				<nut-popup v-model:visible="showImagePopup" position="bottom" safe-area-inset-bottom>
					<div class="nut-picker__bar">
						<div class="nut-picker__title">附件图片</div>
					</div>
					<div class="custom-content">
						<nut-uploader @file-item-click="fileClick" @success="uploadSuccess" @delete="uploadDelete"
							@oversize="oversize" :headers="headers" :data="uploaderData" :maximize="5242880" name="file" maximum="5"
							:url="uploadUrl"></nut-uploader>
					</div>
				</nut-popup>
				<nut-popup v-model:visible="showMoneyPopup" position="bottom" safe-area-inset-bottom>
					<div class="nut-picker__bar">
						<div class="nut-picker__title">选择订单类型</div>
					</div>
					<div class="custom-content">
						<div class="order-type-switch">
							<div class="type-item" :class="{ active: !submitForm.isUrgent }" @click="setOrderType(false)">
								普通订单
							</div>
							<div class="type-item" :class="{ active: submitForm.isUrgent }" @click="setOrderType(true)">
								加急订单
							</div>
						</div>
						
						<div v-if="submitForm.isUrgent" class="urgent-content">
							<div class="tip-text">设置追加金额可提高接单几率</div>
						<nut-input v-model="currAttachMoney" type="digit">
							<template #left>
								<nut-icon name="/static/icons/RMB.png"></nut-icon>
							</template>
		
							<template #right>
									<nut-button @click="onConfimAttachMoney" type="primary" size="small">确认</nut-button>
							</template>
		
						</nut-input>
						</div>
					</div>
				</nut-popup>
				<nut-popup v-model:visible="showCancelTimePopup" position="bottom" safe-area-inset-bottom>
					<nut-picker  :columns="cancelTimes" title="未接单自动取消" @confirm="onConfirmCancelTime"
						@cancel="showCancelTimePopup = false">
					</nut-picker>
				</nut-popup>
				<nut-address
					v-model:visible="showStartAddressPopup" 
					type="exist" 
					:exist-address="startAddressList" 
					:is-show-custom-address="false" 
					@selected="selectedStartAddress" 
					exist-address-title="取货地址">
				</nut-address>
				<nut-address
					v-model:visible="showEndAddressPopup" 
					type="exist" 
					:exist-address="endAddressList" 
					:is-show-custom-address="false" 
					@selected="selectedEndAddress" 
					exist-address-title="送货地址">
				</nut-address>
			</template>
	</scroll-view>
	
</template>
<script setup>

</script>
<script>
	import {upload_url} from '@/request/request.js'
	postSubmitOrder
	import {
		toRaw
	} from "vue";
	// 显式导入InputNumber组件
	import InputNumber from 'nutui-uniapp/components/inputnumber/inputnumber.vue';
	import {
		postSubmitOrder
	} from "@/request/apis/order.js"
	import {
		listSchool
	} from "@/request/apis/school.js"
	import {
		getListTag
	} from "@/request/apis/tag.js"
	import {
		getListAddress
	} from "@/request/apis/address.js"
	import {
		uploadFile
	} from '@/request/request.js'
	import { useNotify } from 'nutui-uniapp/composables';
	export default {
		// 注册InputNumber组件
		components: {
			'nut-input-number': InputNumber
		},
		setup() {
		   const notify = useNotify();
		   const showPrimary = (message) => {notify.primary(message);};
		   const showSuccess = (message) => {notify.success(message);};
		   const showDanger = (message) => {notify.danger(message);};
		   const showWarning = (message) => {notify.warning(message);};
		   const hideNotify = () => {notify.hide();};
		   return {showPrimary,showSuccess,showDanger,showWarning,hideNotify};
		 },
		data() {
			return {
				btnSubmitLoading:false,
				totalPrice:'--',
				uploadUrl:upload_url,
				specifiedTimeText:'尽快配送',
				genderText:'不限',
				uploaderData:{
					type:1,
					name:''
				},
				autoCancelText:'30分钟',
				currAttachMoney:'',
				activeTagIndex:-1,
				selectedTags: [],
				tagQuantities: {},
				tagRemark:'',
				tagList:[
					{
						name:'其它',
						remark:'',
						price: 0
					}
				],
				currSchool:null, //当前校区
				submitForm:{
					serviceType:0 , // 帮取送
					schoolId:null, // 学校id
					weight:'小于2KG',  // 物品重量
					detail:'',  // 订单详情
					tag:'',  // 物品类型
					isTimed:0, // 是否指定配送时间
					specifiedTime:null, // 具体指定配送时间
					autoCancelTtl:1800, // 付款未接单自动取消时间 秒
					gender:2, // 限制性别 2不限
					additionalAmount:0, // 追加金额
					isUrgent:false, // 是否加急订单
					estimatedPrice:0, //商品预估价格
					attachImages:[], // 附件图片的ossid
					attachFiles:[], // 附件文件的ossid
					startAddress:{
						id:'',
						name:'',
						phone:'',
						title:'',
						detail:'',
						lat:'',
						lon:''
					},
					endAddress:{
						id:'',
						name:'',
						phone:'',
						title:'',
						detail:'',
						lat:'',
						lon:''
					},
				},
				headers: {
					Authorization: 'Bearer ' + uni.getStorageSync("token"),
					'Content-Type': 'multipart/form-data',
				},
				ossList: [],
				showGenderPopup: false,
				showDatePopup: false,
				showServiceTypePopup: false,
				showImagePopup: false,
				showMoneyPopup: false,
				showCancelTimePopup: false,
				showStartAddressPopup:false,
				showEndAddressPopup:false,
				genderData: [{
						text: "不限",
						value: 2
					},
					{
						text: "男",
						value: 1
					},
					{
						text: "女",
						value: 0
					}
				],
				minDate: new Date(2020, 0, 1),
				maxDate: new Date(2025, 10, 1),
				currentDate: new Date(2022, 4, 10, 10, 10),
				rangeValue: 2,
				cancelTimes: [{
						text: '30分钟',
						value: 1800
					},
					{
						text: '1小时',
						value: 3600
					},
					{
						text: '2小时',
						value: 7200
					},
					{
						text: '3小时',
						value: 10800
					},
					{
						text: '5小时',
						value: 18000
					},
					{
						text: '10小时',
						value: 36000
					},
					{
						text: '24小时',
						value: 86400
					},
					{
						text: '48小时',
						value: 172800
					}
				],
				startAddressList:[],
				endAddressList:[],
			}
		},
		onLoad() {
			//this.initDate()
			this.initData()
			this.getAddresses()
			this.getTags()
			
			// 初始化为普通订单
			this.submitForm.isUrgent = false;
			this.submitForm.serviceType = 1; // 普通订单serviceType=1
			this.submitForm.additionalAmount = "0"; // 普通订单追加金额为0
			this.currAttachMoney = "0";
			
			this.cacurlatePrice()
		},
		onReachBottom() {

		},
		methods: {
			submitOrder() {
				let that = this
				let form = this.submitForm
				let ossIds = []
				let ossList = this.ossList
				for (var i = 0; i < ossList.length; i++) {
					ossIds.push(ossList[i].ossId)
				}
				form.attachImages = ossList
				
				// 设置正确的serviceType值
				if (form.isUrgent) {
					// 加急订单，使用默认的帮取送类型(0)
					form.serviceType = 0;
				} else {
					// 普通订单
					form.serviceType = 1;
					// 普通订单强制将追加金额设为0，符合后端处理逻辑
					form.additionalAmount = "0";
				}
				
				// 确保追加金额不低于底价
				const floorPrice = Number(this.currSchool.floorPrice);
				
				// 只有加急订单需要验证追加金额
				if (form.isUrgent) {
					// 加急订单，确认金额是否足够
					let minAmount = floorPrice;
					if (this.currSchool.emergencyMinAmount && form.serviceType === 0) {
						minAmount = Number(this.currSchool.emergencyMinAmount);
					}
					
					if (Number(form.additionalAmount) < minAmount) {
						this.showWarning(`加急订单追加金额最低为 ${minAmount} 元`);
						return;
					}
					console.log('加急订单追加金额:', form.additionalAmount);
				}
				
				this.btnSubmitLoading = true
				
				const SUBSCRIBE_ID = 'uQ8cRcy8jM8Rb09EUDZopOZgCLQcrxlFlGNzVez8_-w' // 模板ID
				if (wx.requestSubscribeMessage) {
				  wx.requestSubscribeMessage({
				    tmplIds: [SUBSCRIBE_ID],
				    complete() {
				    	postSubmitOrder(form).then(res => {
				    		console.log('订单提交响应:', res);
				    		
				    		// 获取支付参数，处理不同的字段名
				    		const payData = res.data;
				    		// 后端可能返回package或packageValue字段
				    		if (payData && payData.package && !payData.packageValue) {
				    			payData.packageValue = payData.package;
				    		}
				    		
				    		// 检查响应数据是否完整
				    		if (!payData || !payData.timeStamp || !payData.nonceStr || 
				    		   (!payData.packageValue && !payData.package) || !payData.signType || !payData.paySign) {
				    			console.error('支付参数不完整:', payData);
				    			that.showDanger("支付参数不完整，请重试");
				    			that.btnSubmitLoading = false;
				    			return;
				    		}
				    		
				    		// 检查是否为降级模式
				    		const fallbackPattern = 'wx_fallback_';
				    		const isFallbackMode = (payData.packageValue && payData.packageValue.includes(fallbackPattern)) || 
				    		                      (payData.package && payData.package.includes(fallbackPattern));
				    		
				    		console.log('packageValue:', payData.packageValue || payData.package);
				    		console.log('是否为降级模式:', isFallbackMode);
				    		
				    		if (isFallbackMode) {
				    			console.log('检测到降级支付模式，使用模拟支付流程');
				    			// 在降级模式下，直接模拟支付成功
				    			uni.showModal({
				    				title: '测试环境支付',
				    				content: '当前为测试环境模拟支付，点击确定将直接完成支付',
				    				success: function(modal) {
				    					if (modal.confirm) {
				    						console.log('模拟支付成功');
				    						
				    						// 调用模拟支付成功API
				    						let baseUrl = uni.getStorageSync('baseUrl');
				    						// 如果baseUrl为空，则使用硬编码的备用URL
				    						if (!baseUrl) {
				    							// 由于小程序环境无法直接使用require导入模块，使用硬编码备用URL
				    							baseUrl = 'http://localhost:8081';
				    							console.log('使用硬编码的备用baseUrl:', baseUrl);
				    						}
				    						
				    						console.log('使用的baseUrl:', baseUrl);
				    						
				    						uni.request({
				    							url: baseUrl + '/notify/mockPaySuccess/' + res.data.orderId,
				    							method: 'GET',
				    							success: function(apiRes) {
				    								console.log('模拟支付成功API调用成功', apiRes);
				    								that.showSuccess("支付成功(测试模式)");
				    							},
				    							fail: function(err) {
				    								console.error('模拟支付成功API调用失败', err);
				    								// 即使API调用失败，也显示成功消息继续流程
				    								that.showSuccess("支付成功(测试模式)");
				    								console.log('尽管API调用失败，仍继续模拟支付成功流程');
				    							},
				    							complete: function() {
				    				setTimeout(() => {
				    					uni.redirectTo({
				    						url:"/pages/API/order/detail/detail?id="+res.data.orderId
				    									});
				    								}, 1500);
				    							}
				    						});
				    					} else {
				    						that.showInfo("已取消支付");
				    					}
				    					that.btnSubmitLoading = false;
				    				}
				    			});
				    			return;
				    		}
				    		
				    		// 点击支付
				    		uni.requestPayment({
				    			provider: 'wxpay',
				    			timeStamp: payData.timeStamp, 
				    			nonceStr: payData.nonceStr, 
				    			package: payData.packageValue || payData.package,
				    			signType: payData.signType, 
				    			paySign: payData.paySign, 
				    			success: function (res) {
				    				console.log('支付成功',res);
				    				// 支付成功...
				    			},
				    			fail: function (err) {
				    				console.log('支付失败',err);
				    				that.showDanger("支付失败: " + (err.errMsg || JSON.stringify(err)))
				    			},
				    			complete() {
				    				that.btnSubmitLoading = false
				    			}
				    		});
				    	}).catch(err => {
				    		console.error('订单提交错误:', err);
				    		that.showDanger(typeof err === 'string' ? err : JSON.stringify(err))
				    	}).finally(res => {
				    		that.btnSubmitLoading = false
				    	})
				    }
				  });
				} else {
				  wx.showModal({
				    title: '提示',
				    content: '请更新您微信版本，来获取订阅消息功能',
				    showCancel: false
				  })
				}
				
				
				
				
			},
			cacurlatePrice() {
				let additionalAmount = Number(this.submitForm.additionalAmount)
				let floorPrice = Number(this.currSchool.floorPrice)
				
				// 获取标签价格
				let tagPrice = 0
				
				// 计算所有选中标签的总价
				for (let index of this.selectedTags) {
					const selectedTag = this.tagList[index]
					console.log('计算价格选中的标签:', selectedTag);
					
					// 检查多种可能的价格字段名
					const possiblePriceFields = ['price', 'fee', 'cost', 'tagPrice', 'amount', 'money'];
					let singleTagPrice = 0;
					for(let field of possiblePriceFields) {
						if(selectedTag[field] !== undefined && selectedTag[field] !== null) {
							singleTagPrice = Number(selectedTag[field]);
							console.log(`使用标签价格字段 ${field}:`, singleTagPrice);
							break;
						}
					}
					
					// 计算该标签的总价（单价 × 数量）
					const quantity = this.tagQuantities[index] || 1;
					tagPrice += singleTagPrice * quantity;
				}
				
				console.log('追加金额:', additionalAmount);
				console.log('追加金额底价:', floorPrice);
				console.log('标签价格:', tagPrice);
				
				// 总价格计算 - 严格区分普通订单和加急订单
				let totalPrice = 0;
				
				if (this.submitForm.isUrgent) {
					// 加急订单：标签价格 + 用户设置的追加金额
					totalPrice = (tagPrice + additionalAmount).toFixed(2);
				} else {
					// 普通订单：仅包含标签价格
					totalPrice = (tagPrice).toFixed(2);
				}
				
				console.log('总价格:', totalPrice);
				this.totalPrice = totalPrice
			},
			onGenderConfirm(e) {
				let item = e.selectedOptions[0]
				this.submitForm.gender = item.value
				this.genderText = item.text
				this.showGenderPopup = false
			},
			onConfirmCancelTime(e) {
				let item = e.selectedOptions[0]
				this.submitForm.autoCancelTtl = item.value
				this.autoCancelText = item.text
				this.showCancelTimePopup = false

			},
			setOrderType(isUrgent) {
				// 设置订单类型标志
				this.submitForm.isUrgent = isUrgent;
				
				if (isUrgent) {
					// 加急订单，设置最低追加金额
					const floorPrice = Number(this.currSchool.floorPrice);
					let minAmount = floorPrice;
					if (this.currSchool.emergencyMinAmount && this.submitForm.serviceType === 0) {
						minAmount = Number(this.currSchool.emergencyMinAmount);
						console.log('使用帮取送特殊最低金额:', minAmount);
					}
					
					this.submitForm.additionalAmount = minAmount.toString();
					this.currAttachMoney = minAmount.toString();
				} else {
					// 普通订单，追加金额设为0
					this.submitForm.additionalAmount = "0";
					this.currAttachMoney = "0";
				}
				
				// 重新计算价格
				this.cacurlatePrice();
				
				// 普通订单自动关闭弹窗，加急订单保持打开状态以便用户设置金额
				if (!isUrgent) {
					this.showMoneyPopup = false;
				}
			},
			onConfimAttachMoney() {
				// 如果不是加急订单，不处理追加金额
				if (!this.submitForm.isUrgent) {
					this.showMoneyPopup = false;
					return;
				}
				
				let value = this.currAttachMoney;
				
				// 确保有值
				if (!value || value.trim() === '') {
					// 使用默认的最低金额
					const floorPrice = this.currSchool.floorPrice;
					let minAmount = floorPrice;
					if (this.currSchool.emergencyMinAmount && this.submitForm.serviceType === 0) {
						minAmount = this.currSchool.emergencyMinAmount;
					}
					value = minAmount.toString();
				}
				
				// 标准格式化处理
				// 1. 去除非数字和小数点的字符
				value = value.replace(/[^\d.]/g, "");
				// 2. 确保只有一个小数点
				value = value.replace(/\.{2,}/g, "."); 
				value = value.replace(/^(\d*\.?)|(\d*)\.?/g, "$1$2"); 
				// 3. 限制小数点后两位
				if (value.includes(".")) {
				const parts = value.split(".");
				value = `${parts[0]}.${parts[1].substring(0, 2)}`;
				}
				// 4. 去除前导0（保留"0."形式）
				value = value.replace(/^0+(\d)/, "$1");
				if (value.startsWith(".")) {
					value = "0" + value;
				}
				
				// 验证追加金额是否满足最低要求
				const floorPrice = Number(this.currSchool.floorPrice);
				// 对于加急订单，检查是否有特定的最低金额设置
				let minAmount = floorPrice;
				if (this.currSchool.emergencyMinAmount && this.submitForm.serviceType === 0) {
					console.log('检测到加急订单，最低金额为:', this.currSchool.emergencyMinAmount);
					minAmount = Number(this.currSchool.emergencyMinAmount);
				}
				
				if (parseFloat(value) < parseFloat(minAmount)) {
					this.showWarning(`加急订单追加金额最低为 ${minAmount} 元`);
					return;
				}
				
				// 更新格式化后的值
				this.submitForm.additionalAmount = value;
				this.currAttachMoney = value;
				this.cacurlatePrice();
				
				this.showMoneyPopup = false;
			},
			onTimedConfirm(e) {
				console.log(e);
				let day = e.selectedOptions[2].value
				let hour = e.selectedOptions[3].value
				let minute = e.selectedOptions[4].value
				
				this.submitForm.isTimed = 1
				this.submitForm.specifiedTime = this.parseLocalDateTime(this.currentDate)
				this.specifiedTimeText = day+'日 '+hour+':'+minute
				this.showDatePopup = false
			},
			parseLocalDateTime(date) {
				const localDateTime = date.getFullYear() + "-" +
				    String(date.getMonth() + 1).padStart(2, '0') + "-" +
				    String(date.getDate()).padStart(2, '0') + " " +
				    String(date.getHours()).padStart(2, '0') + ":" +
				    String(date.getMinutes()).padStart(2, '0') + ":" +
				    String(date.getSeconds()).padStart(2, '0');
				return localDateTime
			},
			onTimedCancel() {
				this.submitForm.isTimed = 0
				this.submitForm.specifiedTime = null
				this.specifiedTimeText = '尽快配送'
				this.showDatePopup = false
			},
			confirmTag() {
				// 检查"其它"标签是否被选中且没有输入内容
				if(this.selectedTags.includes(this.tagList.length - 1) && this.submitForm.tag =='') {
					this.showWarning('请先输入物品类型')
					return
				}
				
				// 准备标签名称数组
				let tagNames = [];
				
				// 收集所有选中标签的名称
				for (let index of this.selectedTags) {
					if(index != this.tagList.length - 1) {
						// 如果有数量，添加数量信息
						const quantity = this.tagQuantities[index] || 1;
						const tagName = this.tagList[index].name;
						tagNames.push(quantity > 1 ? `${tagName}×${quantity}` : tagName);
				} else {
					// 用户自定义了标签名称，确保最后一个标签("其它")使用用户输入的名称
						this.tagList[index].name = this.submitForm.tag;
						tagNames.push(this.submitForm.tag);
				}
				}
				
				// 将所有标签名称用逗号连接
				this.submitForm.tag = tagNames.join('，');
				
				// 确认标签选择后重新计算价格
				this.cacurlatePrice()
				this.showServiceTypePopup = false
			},
			toggleTag(index) {
				// 检查标签是否已被选中
				const tagIndex = this.selectedTags.indexOf(index);
				
				if (tagIndex === -1) {
					// 如果未选中，添加到选中列表
					this.selectedTags.push(index);
					// 初始化数量为1
					if (!this.tagQuantities[index]) {
						this.$set(this.tagQuantities, index, 1);
					}
				} else {
					// 如果已选中，从选中列表中移除
					this.selectedTags.splice(tagIndex, 1);
				}
				
				// 显示该标签的备注信息
				this.tagRemark = this.tagList[index].remark;
				
				// 更新当前选中的索引（保留用于兼容）
				this.activeTagIndex = index;
				
				// 选择标签后重新计算价格
				this.cacurlatePrice();
			},
			setActiveTag(index) {
				// 保留原始方法用于兼容性，但调用新方法
				this.toggleTag(index);
			},
			onQuantityChange() {
				// 当标签数量变化时重新计算价格
				this.cacurlatePrice();
			},
			initData(){
				this.currSchool = this.$store.state.currSchool;
				this.submitForm.schoolId = this.currSchool.id
			},
			updateAddress(data,type) {
				if(type == 'start') {
					this.submitForm.startAddress = data
				}
				else if(type == 'end') {
					this.submitForm.endAddress = data
				}
			},
			showStartAddresses(){
				this.showStartAddressPopup = true;
			},
			showEndAddresses(){
				this.showEndAddressPopup = true;
			},
			toAddAddress(type) {
				let data = null
				if(type == 'start') {
					data = JSON.stringify(this.submitForm.startAddress)
				}
				else {
					data = JSON.stringify(this.submitForm.endAddress)
				}
				uni.navigateTo({
					url:'/pages/API/address/add/add?data=' + data + '&type=' + type
				})
			},
			selectedStartAddress(prevExistAdd,nowExistAdd,arr) {
				if(nowExistAdd.provinceName + nowExistAdd.addressDetail == this.submitForm.endAddress.title + '-' +  this.submitForm.endAddress.detail) {
					this.showWarning('取送地址不能相同')
					return
				}
				this.submitForm.startAddress = {
					id:nowExistAdd.id,
					name:nowExistAdd.name,
					phone:nowExistAdd.phone,
					title:nowExistAdd.provinceName,
					detail:nowExistAdd.addressDetail.startsWith('-') ? nowExistAdd.addressDetail.slice(1) : str,
					lat:nowExistAdd.lat,
					lon:nowExistAdd.lon
				}
				
			},
			selectedEndAddress(prevExistAdd,nowExistAdd,arr) {
				if(nowExistAdd.provinceName + nowExistAdd.addressDetail == this.submitForm.startAddress.title + '-' +  this.submitForm.startAddress.detail) {
					this.showWarning('取送地址不能相同')
					return
				}
				this.submitForm.endAddress = {
					name:nowExistAdd.name,
					phone:nowExistAdd.phone,
					title:nowExistAdd.provinceName,
					detail:nowExistAdd.addressDetail.startsWith('-') ? nowExistAdd.addressDetail.slice(1) : str,
					lat:nowExistAdd.lat,
					lon:nowExistAdd.lon
				}
				
			},
			getTags(){
				getListTag({
					schoolId:this.currSchool.id,
					serviceType:this.submitForm.serviceType
				}).then(res => {
					console.log('标签数据:', res);
					// 检查和标准化标签数据
					if(res.data && res.data.length > 0) {
						// 预处理标签数据，确保价格字段正确
						res.data.forEach(tag => {
							// 检查多种可能的价格字段名
							const possiblePriceFields = ['price', 'fee', 'cost', 'tagPrice', 'amount', 'money'];
							let priceField = null;
						for(let field of possiblePriceFields) {
								if(tag[field] !== undefined && tag[field] !== null) {
									priceField = field;
									// 确保price字段存在，用于统一计算
									if(field !== 'price') {
										tag.price = Number(tag[field]);
									}
									break;
							}
						}
							if(!priceField) {
								console.warn('标签缺少价格字段:', tag.name);
								tag.price = 0; // 设置默认价格为0
							}
						});
					}
					this.tagList.unshift(...res.data)
					// 获取标签后重新计算价格
					this.cacurlatePrice()
				}).catch(err => {
					this.showWarning(err)
				})
			},
			getAddresses() {
				getListAddress({pageNum:1,pageSize:20}).then(res => {
					console.log(res);
					let rows = res.data
					this.startAddressList = this.mapFields(rows)
					this.endAddressList = this.mapFields(rows)
				}).catch(err => {
					this.showWarning(err)
				})
			},
			fileClick(file) {
				file = file.fileItem
				console.log(file);
				let imgsArray = [];
				imgsArray[0] = file.url;
				uni.previewImage({
					current: 0,
					urls: imgsArray
				});
			},
			oversize(files) {
				console.log(files);
				this.showWarning('文件大小超出5MB')
			},
			uploadDelete(e) {
				let index = e.index
				this.ossList.splice(index, 1)
			},
			uploadSuccess(e) {
				let data = e.data.data
				data = JSON.parse(data)
				let ossId = data.data.ossId
				this.ossList.push(ossId)
			},
			onWeightChange(e) {
				console.log(e);
				if (e == 2) {
					this.submitForm.weight = '小于2KG'
				} else if (e == 20) {
					this.submitForm.weight = '大于20KG'
				} else {
					this.submitForm.weight = e + 'KG左右'
				}
			},
			showDatePopupClick() {
				this.initDate();
				this.showDatePopup = true
			},
			initDate() {
				const now = new Date();
				// 获取当前分钟数
				const currentMinutes = now.getMinutes();
				// 向上取整到最近的 15 的倍数
				const roundedMinutes = Math.ceil(currentMinutes / 15) * 15;
				// 如果超出 60 分钟，处理进位
				if (roundedMinutes >= 60) {
					now.setHours(now.getHours() + 1);
					now.setMinutes(0); // 分钟归零
				} else {
					now.setMinutes(roundedMinutes); // 设置为向上取整的分钟数
				}

				this.currentDate = now; // 当前时间
				this.minDate = now;
				// 设置最大时间为当前时间 + 7天
				const max = new Date();
				max.setDate(max.getDate() + 7); // 当前时间加7天
				this.maxDate = max;
			},
			mapFields(data) {
				 return data.map((formData) => ({
				    id: formData.id,
				    addressDetail: '-' + formData.detail,
				    cityName: '', // 默认值为空
				    countyName: '', // 默认值为空
				    provinceName: formData.title,
				    selectedAddress: false, 
				    townName: '', // 默认值为空
				    name: formData.name,
				    phone: formData.phone,
					lat:formData.lat,
					lon:formData.lon,
				  }));
			},
		},

	}
</script>

<style lang="scss" scoped>
page {
  background-color: #f7f9ff;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.box {
  margin: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
  
  :deep(.nut-cell) {
    margin: 10rpx 0;
    padding: 24rpx 20rpx;
    
    &::after {
      left: 20rpx;
      right: 20rpx;
      border-bottom: 1px solid #f0f2f5;
    }
    
    &:last-child::after {
      border-bottom: none;
    }
  }
  
  :deep(.nut-cell__title) {
    font-size: 28rpx;
    font-weight: 500;
  }
  
  :deep(.nut-cell__value) {
    color: #666;
  }
  
  :deep(.nut-button--primary) {
    background-color: #4F7DF5;
    border-color: #4F7DF5;
    border-radius: 36rpx;
  }
  
  :deep(.nut-icon) {
    width: 40rpx;
    height: 40rpx;
    margin-right: 12rpx;
  }
  
  :deep(.nut-textarea) {
    border-radius: 16rpx;
    padding: 16rpx;
    background-color: #f7f9ff;
    margin: 16rpx 0;
    border: 1px solid #ebedf0;
  }
}
	
	.orderBtnBox {
		 position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
		  display: flex;
  padding: 0 30rpx;
		  align-items: center;
  z-index: 999;
  
  .boxLeft {
    flex: 1;
    display: flex;
    align-items: baseline;
    
    .boxPrice {
      color: #ff6b6b;
      font-size: 40rpx;
      font-weight: 600;
	}
  }
  
  .boxRight {
    .myBtn {
      background-color: #4F7DF5;
      border: none;
      color: #fff;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 32rpx;
      padding: 0 40rpx;
      border-radius: 40rpx;
      font-weight: 500;
      box-shadow: 0 6rpx 16rpx rgba(79, 125, 245, 0.3);
	}
  }
}

.myImage {
  position: absolute;
  right: 10px;
  top: 10px;
  color: #4F7DF5;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 6rpx 16rpx;
  background-color: rgba(79, 125, 245, 0.1);
  border-radius: 24rpx;
  
  :deep(.nut-icon) {
    margin-right: 8rpx;
  }
}

.custom-content {
  padding: 32rpx;
  
  .nut-tag {
    margin: 12rpx;
    padding: 12rpx 24rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    
    &:not([plain]) {
      background-color: #4F7DF5 !important;
      border-color: #4F7DF5 !important;
	}
	}

  .remark {
    margin: 24rpx 12rpx;
    color: #999;
    font-size: 26rpx;
    line-height: 1.5;
	}

  .range-box {
    margin: 32rpx 12rpx 20rpx;
    
    :deep(.nut-range) {
      margin-top: 40rpx;
    }
    
    :deep(.nut-range__button) {
      width: 40rpx;
      height: 40rpx;
      background-color: #4F7DF5;
	}

    :deep(.nut-range__bar) {
      background-color: #4F7DF5;
    }
  }
  
  :deep(.nut-input) {
    margin-top: 20rpx;
    
    .nut-input__right {
      margin-left: 20rpx;
	}
  }
}

:deep(.nut-popup) {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  
  .nut-picker__bar {
    background-color: #fff;
    
    .nut-picker__title {
      color: #333;
      font-weight: 500;
	}

    .nut-picker__cancel, 
    .nut-picker__confirm {
      color: #4F7DF5;
      font-size: 28rpx;
    }
	}

  .nut-picker {
    background-color: #fff;
	}

  .nut-picker-roller-item {
    font-size: 32rpx;
	}

  .nut-picker-roller-item-hidden {
    color: #ccc;
	}
	}

:deep(.nut-address) {
  .nut-address__header {
    background-color: #fff;
	}

  .nut-address__item {
    background-color: #fff;
  }
  
  .nut-address__item:not(:last-child)::after {
    left: 30rpx;
    right: 30rpx;
	}

  .nut-address__detail {
    color: #666;
  }
  
  .nut-address__item--active {
    color: #4F7DF5;
  }
}

.quantity-selector {
  display: flex;
  align-items: center;
  margin: 16rpx 12rpx;
  padding: 10rpx;
  background-color: rgba(79, 125, 245, 0.1);
  border-radius: 8rpx;
  
  .tag-name {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    margin-right: 12rpx;
	}

  :deep(.nut-inputnumber) {
    --nut-inputnumber-button-width: 50rpx;
    --nut-inputnumber-button-height: 50rpx;
    --nut-inputnumber-input-width: 80rpx;
    --nut-inputnumber-input-font-size: 28rpx;
    --nut-inputnumber-input-background-color: #fff;
    --nut-inputnumber-button-background: #4F7DF5;
    --nut-inputnumber-button-icon-color: #fff;
    --nut-inputnumber-border-radius: 6rpx;
	}
}

.order-type-switch {
  display: flex;
  width: 100%;
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1px solid #ebedf0;
  
  .type-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 30rpx;
    color: #666;
    background-color: #f7f8fa;
    position: relative;
    
    &.active {
      color: #4F7DF5;
      background-color: #ecf2ff;
      font-weight: 500;
      
      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        width: 50%;
        height: 4rpx;
        background-color: #4F7DF5;
        border-radius: 4rpx;
      }
    }
  }
}

.urgent-content {
  margin-top: 20rpx;
  
  .tip-text {
    font-size: 26rpx;
    color: #ff6b6b;
    margin-bottom: 16rpx;
  }
	}
</style>