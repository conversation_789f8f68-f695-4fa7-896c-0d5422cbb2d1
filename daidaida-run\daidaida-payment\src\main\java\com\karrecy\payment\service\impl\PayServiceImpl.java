package com.karrecy.payment.service.impl;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.karrecy.common.exception.PayException;
import com.karrecy.common.utils.BigDecimalUtils;
import com.karrecy.common.utils.StringUtils;
import com.karrecy.payment.domain.vo.PayedVO;
import com.karrecy.payment.properties.WxPayProperties;
import com.karrecy.payment.service.IPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.util.Base64;
import java.util.Random;

/**
 * <p>
 * 支付服务实现
 * </p>
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Primary
public class PayServiceImpl implements IPayService {

    private final WxPayService wxPayService;
    private final WxPayProperties wxPayProperties;
    
    /**
     * 初始化时日志输出配置信息
     */
    @PostConstruct
    public void init() {
        // 打印配置信息，检查是否完整
        if (wxPayProperties.getUsePublicKeyMode()) {
            if (StringUtils.isNotEmpty(wxPayProperties.getWechatPayPublicKey()) && 
                StringUtils.isNotEmpty(wxPayProperties.getWechatPayPublicKeySerialNumber()) &&
                StringUtils.isNotEmpty(wxPayProperties.getMerchantSerialNumber()) &&
                StringUtils.isNotEmpty(wxPayProperties.getPrivateKeyPath())) {
                
                log.info("微信支付公钥模式配置完整，商户号: {}, 商户证书序列号: {}, 公钥序列号: {}", 
                    wxPayProperties.getMchId(), 
                    wxPayProperties.getMerchantSerialNumber(),
                    wxPayProperties.getWechatPayPublicKeySerialNumber());
                    
                // 检查配置对象是否正确设置
                WxPayConfig config = wxPayService.getConfig();
                if (config.getVerifier() != null) {
                    log.info("验证器已正确设置，类型: {}", config.getVerifier().getClass().getName());
                } else {
                    log.warn("验证器未设置，可能导致微信支付API调用失败");
                }
            } else {
                log.warn("微信支付公钥模式配置信息不完整，可能导致支付失败");
                if (StringUtils.isEmpty(wxPayProperties.getWechatPayPublicKey())) {
                    log.warn("未配置微信支付公钥");
                }
                if (StringUtils.isEmpty(wxPayProperties.getWechatPayPublicKeySerialNumber())) {
                    log.warn("未配置微信支付公钥序列号");
                }
                if (StringUtils.isEmpty(wxPayProperties.getMerchantSerialNumber())) {
                    log.warn("未配置商户API证书序列号");
                }
                if (StringUtils.isEmpty(wxPayProperties.getPrivateKeyPath())) {
                    log.warn("未配置商户API证书私钥路径");
                }
            }
        } else {
            log.info("使用平台证书模式，证书路径配置: {}", wxPayProperties.getKeyPath());
        }
        
        // 降级模式提示
        if(wxPayProperties.getEnableFallback()) {
            log.warn("已启用支付降级模式，非生产环境使用");
        }
    }

    /**
     * 支付回调
     */
    @Override
    public String payNotifyV3(String notifyData, HttpServletRequest request) {
        SignatureHeader signatureHeader = this.getRequestHeader(request);
        try{
            // 使用maven包中的方法进行解析与验证
            WxPayNotifyV3Result notifyV3Result =
                    wxPayService.parseOrderNotifyV3Result(notifyData, signatureHeader);
            WxPayNotifyV3Result.DecryptNotifyResult payResult = notifyV3Result.getResult();
            log.info("收到微信支付回调通知: {}", payResult.toString());
            
            //订单支付成功后执行
            if (payResult.getTradeState().equals("SUCCESS")) {
                // 这里直接记录支付成功信息，可以在需要时通过其他方式（如MQ）通知订单服务
                log.info("支付成功，订单ID: {}, 微信支付单号: {}", 
                    payResult.getOutTradeNo(), payResult.getTransactionId());
                
                // 这里可以添加支付成功后的业务逻辑
                // 例如直接调用订单服务的方法更新订单状态
                // orderService.paymentSuccess(Long.valueOf(payResult.getOutTradeNo()));
                
                // 如果启用降级模式，可能需要特殊处理模拟支付
                if (wxPayProperties.getEnableFallback() && 
                    payResult.getOutTradeNo().contains("fallback")) {
                    log.info("降级模式支付回调，标记为成功: {}", payResult.getOutTradeNo());
                }
            } else {
                log.warn("支付未成功，状态: {}, 订单ID: {}", 
                    payResult.getTradeState(), payResult.getOutTradeNo());
            }
        }
        catch (Exception e){
            log.error("【支付回调通知处理失败】: {}", e.getMessage(), e);
            return WxPayNotifyResponse.fail("微信支付-回调处理失败");
        }

        return WxPayNotifyResponse.success("OK");
    }

    /**
     * 获取请求头
     * @param request 请求对象
     * @return 签名头
     */
    private SignatureHeader getRequestHeader(HttpServletRequest request) {
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader("Wechatpay-Signature"));
        signatureHeader.setSerial(request.getHeader("Wechatpay-Serial"));
        signatureHeader.setTimeStamp(request.getHeader("Wechatpay-Timestamp"));
        signatureHeader.setNonce(request.getHeader("Wechatpay-Nonce"));
        return signatureHeader;
    }

    /**
     * 请求微信支付
     * @param desc 订单描述
     * @param orderId 订单ID
     * @param totalAmount 支付金额
     * @param openid 用户openid
     * @return 支付参数
     */
    @Override
    public PayedVO pay(String desc, Long orderId, BigDecimal totalAmount, String openid) {
        // 优先判断是否使用降级逻辑
        if (wxPayProperties.getEnableFallback()) {
            // 直接使用降级支付逻辑，不调用微信支付API
            log.info("启用了支付降级模式，直接使用降级支付逻辑处理，订单ID: {}", orderId);
            return createMockPaymentResponse(orderId, totalAmount);
        }
        
        try {
            // 检查验证器
            WxPayConfig config = wxPayService.getConfig();
            if (config.getVerifier() == null) {
                log.error("微信支付验证器未设置，无法进行支付API调用，切换到降级模式");
                if (wxPayProperties.getEnableFallback()) {
                    return createMockPaymentResponse(orderId, totalAmount);
                } else {
                    throw new PayException("微信支付配置错误：验证器未设置");
                }
            }
            
            WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();

            // 金额不能为0，设置最小值为1分
            // 转换金额为分
            int totalAmountCents = BigDecimalUtils.convertToCents(totalAmount);
            if (totalAmountCents <= 0) {
                totalAmountCents = 1; // 最小金额1分钱
                log.warn("订单金额为0，已设置为最小金额1分，订单ID: {}", orderId);
            }

            request.setDescription(desc);  //订单描述
            request.setOutTradeNo(String.valueOf(orderId)); //订单id
            request.setNotifyUrl(wxPayProperties.getReturnUrl()); // 回调地址
            request.setAmount(new WxPayUnifiedOrderV3Request.Amount()
                    .setTotal(totalAmountCents)
                    .setCurrency("CNY")); // 支付金额，单位分
            request.setPayer(new WxPayUnifiedOrderV3Request.Payer()
                .setOpenid(openid)); // 用户的 openid

            try {
                log.info("开始调用微信支付API，订单ID: {}, 金额: {}分, 回调地址: {}", 
                    orderId, totalAmountCents, wxPayProperties.getReturnUrl());
                
                // 直接使用统一下单接口
                WxPayUnifiedOrderV3Result result = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);
                
                // 获取JSAPI支付参数
                PayedVO payedVO = new PayedVO();
                
                // 手动设置返回数据而不是使用BeanUtils复制
                if (result != null) {
                    log.info("微信支付API调用成功，订单ID: {}, prepayId: {}", orderId, result.getPrepayId());
                    
                    // 手动设置可能存在的属性
                    payedVO.setAppId(wxPayProperties.getAppId());
                    payedVO.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
                    payedVO.setNonceStr(generateRandomString(32));
                    payedVO.setPackageValue("prepay_id=" + result.getPrepayId());
                    payedVO.setSignType("RSA");
                    
                    // 需要手动计算签名，因为SDK的result.getPaySign()方法可能不存在
                    String signContent = calculateSignContent(payedVO.getAppId(), payedVO.getTimeStamp(), 
                        payedVO.getNonceStr(), payedVO.getPackageValue());
                    payedVO.setPaySign(signContent);
                } else {
                    log.error("微信支付API调用返回为空，订单ID: {}", orderId);
                    throw new PayException("微信支付API调用返回为空");
                }
                
                // 确保appId始终被设置，即使result为null
                if (payedVO.getAppId() == null) {
                    payedVO.setAppId(wxPayProperties.getAppId());
                    log.warn("支付参数中appId为null，已重新设置: {}", wxPayProperties.getAppId());
                }
                
                payedVO.setOrderId(orderId);
                payedVO.setTotalFee(totalAmountCents);
                
                // 确保设置total_fee参数，这是前端JSAPI需要的
                if (payedVO.getTotalFee() == null) {
                    payedVO.setTotalFee(totalAmountCents);
                }
                
                log.info("支付参数构建完成: {}", payedVO);
                return payedVO;
            } catch (WxPayException e) {
                log.error("微信支付请求异常: {}, 异常类型: {}", e.getMessage(), e.getClass().getName());
                
                // 特殊处理无可用平台证书错误
                if (e.getMessage().contains("无可用的平台证书") || e.getMessage().contains("v3请求构造异常")) {
                    log.error("微信支付异常: {}", e.getMessage());
                    
                    if (e.getMessage().contains("无可用的平台证书")) {
                        log.error("请确认微信商户平台中已开通API证书或申请公钥验证模式");
                        log.error("公钥模式配置指南: https://pay.weixin.qq.com/docs/merchant/products/platform-certificate/wxp-pub-key-guide.html");
                    }
                    
                    // 检查是否已配置公钥模式
                    if (StringUtils.isEmpty(wxPayProperties.getWechatPayPublicKey()) ||
                        StringUtils.isEmpty(wxPayProperties.getWechatPayPublicKeySerialNumber())) {
                        log.error("未配置微信支付公钥和公钥序列号，请参考文档配置公钥模式");
                    }
                    
                    // 如果启用降级模式，使用降级逻辑
                    if (wxPayProperties.getEnableFallback()) {
                        log.warn("使用支付降级逻辑处理，订单ID: {}", orderId);
                        return createMockPaymentResponse(orderId, totalAmount);
                    }
                }
                
                throw new PayException("微信支付请求异常: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("支付处理异常: {}, 异常类型: {}", e.getMessage(), e.getClass().getName());
            
            // 判断是否启用降级处理
            if (wxPayProperties.getEnableFallback()) {
                log.warn("使用支付降级逻辑处理，订单ID: {}", orderId);
                return createMockPaymentResponse(orderId, totalAmount);
            }
            
            throw new PayException("支付处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建模拟支付响应 (降级模式使用)
     */
    private PayedVO createMockPaymentResponse(Long orderId, BigDecimal totalAmount) {
        PayedVO payedVO = new PayedVO();
        
        // 设置必要参数
        payedVO.setAppId(wxPayProperties.getAppId());
        payedVO.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
        payedVO.setNonceStr(generateRandomString(32));
        
        // 明确标记为降级模式prepay_id
        payedVO.setPackageValue("prepay_id=wx_fallback_" + orderId + "_" + System.currentTimeMillis());
        
        // 设置签名类型和签名值
        payedVO.setSignType("RSA");
        
        // 生成一个有效的签名格式
        String content = "wx_fallback_" + orderId + "_" + generateRandomString(32);
        payedVO.setPaySign(content);
        
        // 设置订单ID和金额
        payedVO.setOrderId(orderId);
        
        // 将金额转换为分
        int totalAmountCents = BigDecimalUtils.convertToCents(totalAmount);
        if (totalAmountCents <= 0) {
            totalAmountCents = 1; // 最小1分钱
        }
        payedVO.setTotalFee(totalAmountCents);
        
        log.info("创建模拟支付响应: {}", payedVO);
        
        // 如果需要自动模拟支付成功，可以在此处添加
        mockPaymentSuccess(orderId);
        
        return payedVO;
    }
    
    /**
     * 模拟支付成功 (测试用)
     */
    private void mockPaymentSuccess(Long orderId) {
        // 仅在开发或测试环境使用
        if (!wxPayProperties.getEnableFallback()) {
            return;
        }
        
        // 这里可以通过其他方式（如直接调用、MQ消息等）触发支付成功流程
        log.info("模拟微信支付成功，订单ID: {}", orderId);
        
        // 可以直接调用订单服务的支付成功处理方法
        // 例如: orderService.paymentSuccess(orderId);
        
        // 或者通过其他方式通知系统支付已成功
        // TODO: 实现具体的模拟支付成功逻辑
    }
    
    /**
     * 计算签名内容
     */
    private String calculateSignContent(String appId, String timeStamp, String nonceStr, String packageValue) {
        try {
            // 获取支付配置中的私钥
            WxPayConfig payConfig = wxPayService.getConfig();
            PrivateKey privateKey = payConfig.getPrivateKey();
            
            if (privateKey == null) {
                log.warn("未找到私钥，使用备用签名方法");
                return calculateBackupSignContent(appId, timeStamp, nonceStr, packageValue);
            }
            
            // 构建签名字符串，格式按微信支付要求
            String signContent = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";
            
            // 使用私钥和SHA256WithRSA算法签名
            java.security.Signature sign = java.security.Signature.getInstance("SHA256withRSA");
            sign.initSign(privateKey);
            sign.update(signContent.getBytes(StandardCharsets.UTF_8));
            byte[] signature = sign.sign();
            
            // Base64编码
            return Base64.getEncoder().encodeToString(signature);
        } catch (Exception e) {
            log.error("计算签名失败，使用备用签名方法", e);
            return calculateBackupSignContent(appId, timeStamp, nonceStr, packageValue);
        }
    }
    
    /**
     * 备用签名计算方法
     */
    private String calculateBackupSignContent(String appId, String timeStamp, String nonceStr, String packageValue) {
        try {
            // 简单的签名计算方法，生产环境不推荐使用
            String content = appId + timeStamp + nonceStr + packageValue;
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            
            // 转换为十六进制
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            // 如果发生异常，返回一个随机签名
            return "sign_" + generateRandomString(32);
        }
    }

    /**
     * 生成指定长度的随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
    
    /**
     * 请求微信支付
     * @param orderId 订单ID
     * @param totalAmount 总金额
     * @param refundAmount 退款金额
     */
    @Override
    public void refund(Long orderId, BigDecimal totalAmount, BigDecimal refundAmount) {
        try {
            // 判断是否使用降级逻辑
            if (wxPayProperties.getEnableFallback()) {
                log.info("启用了支付降级模式，跳过实际退款API调用，订单ID: {}", orderId);
                return;
            }
            
            WxPayRefundV3Request refundRequest = new WxPayRefundV3Request();
            WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
            amount.setRefund(BigDecimalUtils.convertToCents(refundAmount));
            amount.setTotal(BigDecimalUtils.convertToCents(totalAmount));
            amount.setCurrency("CNY");
            refundRequest.setAmount(amount);
            refundRequest.setNotifyUrl(wxPayProperties.getRefundUrl());
            refundRequest.setOutTradeNo(String.valueOf(orderId));
            refundRequest.setOutRefundNo(String.valueOf(orderId)); // 退款单号 每个订单最多退款一次
            
            log.info("开始调用微信退款API，订单ID: {}, 退款金额: {}分, 回调地址: {}", 
                orderId, BigDecimalUtils.convertToCents(refundAmount), wxPayProperties.getRefundUrl());

            // 调用接口
            WxPayRefundV3Result result = wxPayService.refundV3(refundRequest);
            log.info("退款结果: {}", result);
        } catch (Exception e) {
            log.error("微信退款异常: {}", e.getMessage(), e);
            
            // 判断是否启用降级处理
            if (wxPayProperties.getEnableFallback()) {
                log.warn("支付降级模式已启用，忽略退款错误，订单ID: {}", orderId);
                return;
            }
            
            throw new PayException("微信退款异常: " + e.getMessage());
        }
    }
    
    /**
     * 退款成功回调
     * @param notifyData 回调数据
     * @param request HTTP请求
     * @return 处理结果
     */
    @Override
    public String refundNotify(String notifyData, HttpServletRequest request) {
        try {
            // 解析退款回调通知
            SignatureHeader signatureHeader = this.getRequestHeader(request);
            log.info("退款回调数据: {}, 签名信息: {}", notifyData, signatureHeader);
            
            // 此处处理退款回调逻辑

            return WxPayNotifyResponse.success("OK");
        } catch (Exception e) {
            log.error("退款回调处理异常: {}", e.getMessage(), e);
            return WxPayNotifyResponse.fail("处理失败");
        }
    }
}
