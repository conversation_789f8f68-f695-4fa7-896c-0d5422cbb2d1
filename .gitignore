# IDEA / Eclipse / VSCode 等编辑器配置
.idea/
.vscode/
*.iml
.classpath
.project
.settings/

# Node.js 相关
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
pnpm-lock.yaml
package-lock.json

# Vue / React / 前端打包文件
*.log
*.tsbuildinfo
/.nuxt
/.output
/out/
/.cache/
/coverage/
/public/build/
/public/dist/
/build/
/dist/
/.serve/
/.eslintcache

# Java / Spring Boot 相关
*.class
*.jar
*.war
*.ear
*.log
*.swp
**/target/
**/src/main/resources/cert/
/bin/
/build/
/out/
/logs/

# Gradle 相关
.gradle/
/build/
/!gradle-wrapper.jar

# Maven 相关
.mvn/
**/pom.xml.tag
**/pom.xml.releaseBackup
**/pom.xml.next
**/pom.xml.bak
release.properties

# Lombok
*.lombok

# Mac / Linux / Windows 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 环境变量 / 配置文件
.env
.env.local
.env.*.local
config/application.yml
config/application-*.yml
secrets/


