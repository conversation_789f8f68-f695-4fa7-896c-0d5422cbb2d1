--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 10
    # 密码(如没有密码请注释掉)
    # password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

# 微信支付配置（开发环境）
wx:
  pay:
    # 小程序或公众号AppID (需要替换为实际值)
    app-id: weixin-java-pay
    # 商户号 (需要替换为实际值)
    mch-id: 1716808176
    # 商户APIv3密钥 (需要替换为实际值)
    api-v3-key: daidaidapaotuigongzuoshi99999999
    # 商户API证书序列号 (需要替换为实际值)
    merchant-serial-number: 2D0B3C8B8CF88CBC8BB8F223B764FF270D190E67
    # 商户私钥路径
    private-key-path: classpath:cert/apiclient_key.pem
    # 微信支付公钥 (需要替换为实际值，包括BEGIN和END行)
    wechat-pay-public-key: |
      -----BEGIN PUBLIC KEY-----
        MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk422l9a172E3RdgOQaJ8
        v/dcbTVYo8++7lyKXxNnmB5CjY2Am1SuW51N1MJ7YqPLz/LhYeRfqjQYzQFo9SZR
        2YA8Bs8MrXrNszlSvj+uwR6oNB+RPE66vOIZCwnL0RZ81FOt1pbA8zmQZg36cz+L
        6rqgbWr4+FBSetz845v6NZimjl2StjJ3cmlEoh8fvskWg35rO8pPgCidBx0Kn3C8
        w1kkHPhqhn7ObTymcpvFhJ4rn2i2k+aRyENcpssRUbbwfOUiAod2+12X3M/u0y28
        lcrNLXKqQsLyr2w8ZaN090zD79l17h8x9KIa6g6KcH7qqLYWI5R3wk5TBvCcSaVe
        RQIDAQAB
      -----END PUBLIC KEY-----
    # 微信支付公钥序列号 (需要替换为实际值，格式如PUB_KEY_ID_XXXXXX)
    wechat-pay-public-key-serial-number: PUB_KEY_ID_0117168081762025051200451919002001
    # 回调地址
    return-url: https://localhost/dev/notify/payNotify
    # 退款回调地址
    refund-url: https://localhost/dev/notify/refundNotify
    # 是否使用公钥模式 (推荐true)
    use-public-key-mode: true
    # 是否启用降级策略 (开发环境建议开启)
    enable-fallback: false
    
    # 以下是传统平台证书模式的配置，使用公钥模式时可以不设置
    # key-path: classpath:cert/apiclient_cert.p12
    # private-cert-path: classpath:cert/apiclient_cert.pem