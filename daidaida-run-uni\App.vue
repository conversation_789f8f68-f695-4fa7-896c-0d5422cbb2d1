<!-- 注意这里的 lang="scss"，并且没有 scoped -->
<style lang="scss">
	@import "nutui-uniapp/styles/index.scss"; 
	
	/* 全局样式变量 */
	:root {
	  --primary-color: #4F7DF5;
	  --success-color: #42b983;
	  --warning-color: #ffaa00;
	  --danger-color: #ff6b6b;
	  --text-color: #333333;
	  --text-color-secondary: #666666;
	  --text-color-light: #999999;
	  --background-color: #f7f9ff;
	  --border-color: #ebedf0;
	  --border-radius: 12rpx;
	  --box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}
	
	/* 基础样式重置 */
	page {
	  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	  background-color: var(--background-color);
	  color: var(--text-color);
	  font-size: 28rpx;
	  line-height: 1.5;
	  box-sizing: border-box;
	}
	
	/* 全局样式定义 */
	view, text, button, input, textarea {
	  box-sizing: border-box;
	}
	
	/* NutUI组件全局样式覆盖 */
	.nut-button--primary {
	  background-color: var(--primary-color);
	  border-color: var(--primary-color);
	}
	
	/* 常用布局类 */
	.flex-row {
	  display: flex;
	  flex-direction: row;
	}
	
	.flex-column {
	  display: flex;
	  flex-direction: column;
	}
	
	.flex-center {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}
	
	.flex-between {
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
	}
	
	.flex-1 {
	  flex: 1;
	}
	
	/* 常用间距类 */
	.m-10 { margin: 10rpx; }
	.m-20 { margin: 20rpx; }
	.m-30 { margin: 30rpx; }
	
	.mt-10 { margin-top: 10rpx; }
	.mt-20 { margin-top: 20rpx; }
	.mt-30 { margin-top: 30rpx; }
	
	.mb-10 { margin-bottom: 10rpx; }
	.mb-20 { margin-bottom: 20rpx; }
	.mb-30 { margin-bottom: 30rpx; }
	
	.p-10 { padding: 10rpx; }
	.p-20 { padding: 20rpx; }
	.p-30 { padding: 30rpx; }
	
	/* 常用颜色类 */
	.text-primary { color: var(--primary-color); }
	.text-success { color: var(--success-color); }
	.text-warning { color: var(--warning-color); }
	.text-danger { color: var(--danger-color); }
	.text-gray { color: var(--text-color-light); }
	
	/* 常用字体大小类 */
	.font-sm { font-size: 24rpx; }
	.font-md { font-size: 28rpx; }
	.font-lg { font-size: 32rpx; }
	.font-xl { font-size: 36rpx; }
	.font-xxl { font-size: 40rpx; }
	
	/* 常用阴影效果 */
	.shadow-sm {
	  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.shadow {
	  box-shadow: var(--box-shadow);
	}
	
	.shadow-lg {
	  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
	}
	
	/* 边框及圆角 */
	.rounded {
	  border-radius: var(--border-radius);
	}
	
	.rounded-lg {
	  border-radius: 24rpx;
	}
	
	.rounded-circle {
	  border-radius: 50%;
	}
	
	/* 卡片容器 */
	.card {
	  background-color: #fff;
	  border-radius: var(--border-radius);
	  box-shadow: var(--box-shadow);
	  padding: 24rpx;
	  margin-bottom: 20rpx;
	}
</style>
<script>
	import {checkLogin,xcxLogin,getInfo,login} from "@/request/apis/login.js";
	import {getSchool} from "@/request/apis/school.js";
	
	export default {
	
		onLaunch: function() {
			console.log('App Launch')
			login.call(this)
			
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			checkSchool() {
				let school = uni.getStorageSync("currentSchool")
				if(school == null || school == undefined || school == '') {
					uni.navigateTo({
						url:"/pages/API/school/select/select"
					})
				}
				else {
					getSchool(school.id).then(res => {
						console.log('学校信息:', res.data);
						// 检查加急订单最低金额是否存在
						if (res.data.emergencyMinAmount) {
							console.log('加急订单最低追加金额:', res.data.emergencyMinAmount);
						} else {
							console.log('未设置加急订单最低追加金额');
						}
						this.$store.commit('setSchool', res.data);
						
						uni.setStorageSync("currentSchool",res.data)
					})
				}
				this.$store.commit('setAppLaunch', true);
				
			},
			async login(){
				let isLogined = await checkLogin();
				console.log(isLogined);
				if(!isLogined.data) {
					uni.showLoading({
						title:"登陆中...",
						duration:2000
					});
					let code = await this.getCode();
					let loginRes = await xcxLogin({"xcxCode":code});
					let token = loginRes.data.token
					uni.setStorageSync("token",token)
				}
				let info = await getInfo();
				console.log(info);
				this.$store.commit('login', info.data.user);
				this.$store.commit('setConfig', info.data.config);
				this.checkSchool()
			},
			async getCode() {
				const code = await getLoginCode();
				return code;
			}
		
			
		},
		
	}
	const getLoginCode = () => {
	  return new Promise((resolve, reject) => {
	    uni.login({
	      success: (res) => {
	        if (res.code) {
	          resolve(res.code);  // 返回 code
	        } else {
	          reject('获取 code 失败');
	        }
	      },
	      fail: (err) => {
	        reject('登录失败: ' + err.errMsg);  // 错误处理
	      },
	    });
	  });
	};
</script>


