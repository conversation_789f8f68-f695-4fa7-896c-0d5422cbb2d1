//是否已经连接上ws
let isOpenSocket = false
//心跳间隔，单位毫秒
let heartBeatDelay = 3000
let heartBeatInterval = null
//心跳时发送的消息文本
let heartBeatText = "PING"
//最大重连次数
let reconnectTimes = 10
let reconnectInterval = null
//重连间隔，单位毫秒
let reconnectDelay = 3000
import { ws_url } from "./request"
let socketTask = null

// 是否正在初始化WebSocket
let isInitializing = false

//连接并打开之后可重连，且只执行重连方法一次
let canReconnect = false

//封装的对象，最后以模块化向外暴露
let ws = {
    socketTask: null,
    init,
    completeClose,
    send,
    reconnect,
    getStatus() {
        return {
            isConnected: isOpenSocket,
            isInitializing: isInitializing
        }
    }
}

// 确保安全关闭当前连接
function safeCloseCurrentSocket() {
    isOpenSocket = false
    clearInterval(heartBeatInterval)
    clearInterval(reconnectInterval)
    
    if (socketTask) {
        try {
            console.log("安全关闭已有WebSocket连接")
            socketTask.close({
                success: () => {
                    console.log("已有WebSocket连接关闭成功")
                    socketTask = null
                },
                fail: () => {
                    console.log("已有WebSocket连接可能已关闭")
                    socketTask = null
                },
                complete: () => {
                    console.log("已有WebSocket连接关闭完成")
                }
            })
        } catch (e) {
            console.error("关闭WebSocket异常:", e)
            socketTask = null
        }
    }
}

function init() {
    // 防止重复初始化
    if (isInitializing) {
        console.log("WebSocket正在初始化中，请勿重复调用")
        return
    }
    
    isInitializing = true
    
    // 先安全关闭已有连接
    safeCloseCurrentSocket()
    
    // 延迟一点时间确保之前的连接已关闭
    setTimeout(() => {
        try {
            console.log("开始创建新的WebSocket连接")
            
            socketTask = uni.connectSocket({
                url: ws_url,
                header:{
                    'Authorization':'Bearer ' + uni.getStorageSync("token")
                },
                complete: (res) => {
                    console.log("WebSocket连接完成", res)
                    isInitializing = false
                }
            })
            
            socketTask.onOpen((res) => {
                console.log("WebSocket连接已打开", res)
                clearInterval(heartBeatInterval)
                clearInterval(reconnectInterval)
                isOpenSocket = true
                canReconnect = true
                //开启心跳机制
                heartBeat()
                
                // 通知连接已建立
                uni.$emit('ws-connected', true)
            })
            
            socketTask.onMessage((res) => {
                try {
                    let result = JSON.parse(res.data)
                    console.log('收到服务器消息:', result)
                    
                    // 检查是否是图片消息，并确保URL正确
                    if (result && result.msgType === 2 && result.message) {
                        // 检查图片URL是否需要修正
                        if (!result.message.startsWith('http')) {
                            // 尝试获取基础URL来修正
                            const baseUrl = getBaseUrl();
                            if (baseUrl) {
                                // 修正URL
                                result.message = `${baseUrl}${result.message}`;
                                console.log('已修正图片URL:', result.message);
                            }
                        }
                    }
                    
                    // 触发全局事件，将消息传递到页面
                    uni.$emit('ws-message', result)
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e)
                }
            })
            
            socketTask.onClose((e) => {
                console.log("WebSocket连接关闭", e)
                isOpenSocket = false
                
                // 通知连接已断开
                uni.$emit('ws-connected', false)
                
                if(canReconnect) {
                    console.log("WebSocket尝试自动重连")
                    reconnect()
                    canReconnect = false
                }
            })
            
            socketTask.onError((e) => {
                console.error("WebSocket连接错误", e)
                isOpenSocket = false
                isInitializing = false
                
                // 通知连接错误
                uni.$emit('ws-error', e)
                
                if(canReconnect) {
                    setTimeout(() => {
                        console.log("WebSocket尝试从错误中恢复")
                        reconnect()
                    }, 1000)
                    canReconnect = false
                }
            })
            
            ws.socketTask = socketTask
        } catch (e) {
            console.error("WebSocket初始化失败:", e)
            isInitializing = false
            setTimeout(() => {
                reconnect()
            }, reconnectDelay)
        }
    }, 500) // 等待500ms确保之前的连接已关闭
}

function heartBeat() {
    // 先清除可能存在的心跳
    clearInterval(heartBeatInterval)
    
    heartBeatInterval = setInterval(() => {
        if (isOpenSocket && socketTask) {
            try {
                // 创建一个符合ChatBody格式的心跳消息，避免服务端空指针异常
                const heartbeatMsg = {
                    orderId: "0",
                    isBroadcast: 0,
                    recipientIds: [],
                    msgType: 0, // 使用特殊类型0标识心跳
                    message: "ping",
                    senderId: uni.getStorageSync("uid") || "0",
                    senderType: uni.getStorageSync("userType") || "0",
                    createTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
                };
                
                socketTask.send({
                    data: JSON.stringify(heartbeatMsg),
                    fail: (err) => {
                        console.error("心跳发送失败:", err)
                        if (canReconnect) {
                            reconnect()
                            canReconnect = false
                        }
                    }
                })
            } catch (e) {
                console.error("发送心跳时出错:", e)
                clearInterval(heartBeatInterval)
            }
        } else {
            // 如果不再连接，停止心跳
            clearInterval(heartBeatInterval)
        }
    }, heartBeatDelay)
}

// 发送消息
function send(value) {
    // 检查连接状态
    if (!isOpenSocket || !socketTask) {
        console.warn("WebSocket未连接，尝试重连后发送消息");
        
        // 重连前保存需要发送的消息
        const pendingMessage = value;
        
        // 如果已经在初始化中，不要再次初始化
        if (!isInitializing) {
            reconnect();
        }
        
        // 2秒后检查连接状态并尝试发送
        setTimeout(() => {
            if (isOpenSocket && socketTask) {
                console.log("延迟检查：WebSocket已连接，发送待发消息");
                doSend(pendingMessage);
            } else {
                console.log("延迟检查：WebSocket仍未连接，返回发送失败");
                // 触发发送失败事件，让上层应用处理
                uni.$emit('ws-send-failed', pendingMessage);
            }
        }, 2000);
        
        return;
    }
    
    doSend(value);
}

// 实际发送消息
function doSend(value) {
    try {
        // 增加发送前的连接检查
        if (!isOpenSocket || !socketTask) {
            console.error("发送时检测到WebSocket未连接");
            uni.$emit('ws-send-failed', value);
            return;
        }
        
        socketTask.send({
            data: value,
            success(res) {
                console.log("消息发送成功");
            },
            fail(err) {
                console.error("消息发送失败:", err);
                uni.$emit('ws-send-failed', value);
            }
        });
    } catch (e) {
        console.error("发送消息时发生异常:", e);
        uni.$emit('ws-send-failed', value);
        
        // 发送异常时尝试重连
        if (canReconnect) {
            setTimeout(() => {
                reconnect();
                canReconnect = false;
            }, 500);
        }
    }
}

function reconnect() {
    // 停止心跳
    clearInterval(heartBeatInterval)
    
    // 清除之前的重连定时器
    clearInterval(reconnectInterval)
    
    // 确保之前的连接已关闭
    safeCloseCurrentSocket()
    
    console.log("开始WebSocket重连流程")
    
    // 重连流程
    if (!isOpenSocket && !isInitializing) {
        let count = 0
        reconnectInterval = setInterval(() => {
            if (isOpenSocket) {
                console.log("已连接成功，停止重连")
                clearInterval(reconnectInterval)
                return
            }
            
            console.log("正在尝试重连", count + 1, "/", reconnectTimes)
            
            if (!isInitializing) {
                init()
            }
            
            count++
            
            // 重连一定次数后就不再重连
            if(count >= reconnectTimes) {
                clearInterval(reconnectInterval)
                console.log("网络异常或服务器错误，重连失败")
                uni.showToast({
                    title: '网络连接错误，请重新进入',
                    icon: 'none',
                    duration: 2000
                })
                
                // 通知重连失败
                uni.$emit('ws-reconnect-failed')
            }
        }, reconnectDelay)
    }
}

function completeClose() {
    console.log("完全关闭WebSocket连接")
    
    // 先将心跳与重连的定时器清除
    clearInterval(heartBeatInterval)
    clearInterval(reconnectInterval)
    
    // 标记为不可重连
    canReconnect = false
    
    // 关闭连接
    safeCloseCurrentSocket()
    
    // 确保这些变量被正确重置
    isOpenSocket = false
    isInitializing = false
    socketTask = null
    ws.socketTask = null
}

// 获取基础URL的辅助函数
function getBaseUrl() {
    // 先尝试从uni全局对象获取
    if (uni.getStorageSync('baseUrl')) {
        return uni.getStorageSync('baseUrl');
    }
    
    // 使用当前页面域名
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.$vm && currentPage.$vm.$baseUrl) {
            return currentPage.$vm.$baseUrl;
        }
    }
    
    // 使用默认值
    if (ws_url) {
        // 从WebSocket URL提取域名部分
        const match = ws_url.match(/^ws(s?):\/\/([^\/]+)/);
        if (match) {
            return `http${match[1]}://${match[2]}`;
        }
    }
    
    // 最后的备选方案是使用API基础URL
    return uni.getStorageSync('apiBaseUrl') || '';
}

export default ws