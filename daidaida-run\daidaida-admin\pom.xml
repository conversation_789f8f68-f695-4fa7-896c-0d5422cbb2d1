<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daidaida-run</artifactId>
        <groupId>com.karrecy</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>daidaida-admin</artifactId>

    <description>
        服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-framework</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-system</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-order</artifactId>
            <version>${revision}</version>
        </dependency>


        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-payment</artifactId>
            <version>${revision}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.ruoyi</groupId>-->
<!--            <artifactId>ruoyi-oss</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; 代码生成&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.ruoyi</groupId>-->
<!--            <artifactId>ruoyi-generator</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;  demo模块  &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.ruoyi</groupId>-->
<!--            <artifactId>ruoyi-demo</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- skywalking 整合 logback -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
