<template>
	<view class="profile-container">
		<!-- 顶部个人信息卡片 -->
		<view class="profile-header">
			<view class="header-bg"></view>
			<view class="avatar-container">
				<nut-uploader 
					@success="uploadSuccess"
					@oversize="oversize" 
					:headers="headers" 
					:data="uploaderData" 
					:maximize="5242880" 
					name="file" 
					accept="image/*"
					:url="uploadUrl"
					:is-preview-full-image="false"
				>
					<view class="avatar-wrapper">
						<image class="avatar-image" :src="updateForm.avatar"></image>
						<view class="avatar-edit">
							<nut-icon name="photograph" size="16"></nut-icon>
						</view>
					</view>
			</nut-uploader>
			</view>
			<view class="user-info">
			   		<nut-input
					class="nickname-input"
					max-length="8"
					  type="nickname"
					  v-model="updateForm.nickname"
						 placeholder="请输入昵称"
				></nut-input>
				<view class="user-status">
					<nut-tag type="primary" v-if="userInfo.userWx.isRunner == 1">跑腿员</nut-tag>
					<nut-tag type="default" v-else>普通用户</nut-tag>
				</view>
			</view>
		</view>
		
		<!-- 手机号信息卡片 -->
		<view class="info-card">
			<view class="card-header">
				<nut-icon name="location2" size="18"></nut-icon>
				<text>手机号码</text>
			</view>
			<view class="card-body">
				<view class="phone-section">
					<view class="phone-number">{{ userInfo.userWx.phone || '未绑定手机号' }}</view>
					<nut-button 
						v-if="showPhoneButton"
						:loading="btnPhoneLoading" 
						@getphonenumber="getPhoneNumber"  
						open-type="getPhoneNumber" 
						type="primary" 
						size="small"
					>绑定手机号</nut-button>
					<nut-button 
						v-else 
						@click="getPhoneNumber('哇哈哈')"   
						type="primary" 
						size="small"
					>绑定手机号</nut-button>
				</view>
			</view>
		</view>
		
		<!-- 用户详细信息卡片 -->
		<view class="info-card">
			<view class="card-header">
				<nut-icon name="my2" size="18"></nut-icon>
				<text>个人信息</text>
			</view>
			<view class="card-body">
				<view class="info-item">
					<text class="item-label">身份类型</text>
					<text class="item-value">{{ userInfo.userWx.isRunner == 1 ? '跑腿员' : '普通用户' }}</text>
				</view>
				<view class="info-item" v-if="userInfo.userWx.isRunner == 1">
					<text class="item-label">跑腿学校</text>
					<text class="item-value">{{ userInfo.userWx.schoolName }}</text>
				</view>
				<view class="info-item" v-if="userInfo.userWx.isRunner == 1">
					<text class="item-label">接单状态</text>
					<nut-tag :type="userInfo.userWx.canTake == 1 ? 'success' : 'danger'">
						{{ userInfo.userWx.canTake == 1 ? '可接单' : '不可接单' }}
					</nut-tag>
				</view>
				<view class="info-item" v-if="userInfo.userWx.isRunner == 1">
					<text class="item-label">真实姓名</text>
					<text class="item-value">{{ userInfo.userWx.realname }}</text>
				</view>
				<view class="info-item" v-if="userInfo.userWx.isRunner == 1">
					<text class="item-label">性别</text>
					<text class="item-value">{{ userInfo.userWx.gender == 1 ? '男' : '女' }}</text>
				</view>
				<view class="info-item">
					<text class="item-label">下单状态</text>
					<nut-tag :type="userInfo.userWx.canOrder == 1 ? 'success' : 'danger'">
						{{ userInfo.userWx.canOrder == 1 ? '可下单' : '不可下单' }}
					</nut-tag>
				</view>
			</view>
		</view>
		
		<!-- 账户信息卡片 -->
		<view class="info-card">
			<view class="card-header">
				<nut-icon name="date" size="18"></nut-icon>
				<text>账户信息</text>
			</view>
			<view class="card-body">
				<view class="info-item">
					<text class="item-label">用户ID</text>
					<text class="item-value id-value">{{ userInfo.uid }}</text>
				</view>
				<view class="info-item">
					<text class="item-label">注册时间</text>
					<text class="item-value">{{ userInfo.createTime }}</text>
				</view>
			</view>
		</view>
		
		<!-- 底部保存按钮 -->
		<view class="save-btn-container">
			<nut-button 
				:loading="btnSubmitLoading" 
				@click="updateProfile" 
				block 
				type="primary"
			>保存修改</nut-button>
		</view>
	</view>
	
	<nut-notify></nut-notify>
</template>

<script setup>

</script>
<script>
	import {upload_url} from '@/request/request.js'
	import { toRaw } from "vue";
import {listSchool} from "@/request/apis/school.js"
import {putUpdateProfile,getBindPhone,getCanReqPhone} from "@/request/apis/user.js"
import {getInfo} from "@/request/apis/login.js"
	import { useNotify } from 'nutui-uniapp/composables';
	export default {
		setup() {
		   const notify = useNotify();
		   const showPrimary = (message) => {notify.primary(message);};
		   const showSuccess = (message) => {notify.success(message);};
		   const showDanger = (message) => {notify.danger(message);};
		   const showWarning = (message) => {notify.warning(message);};
		   const hideNotify = () => {notify.hide();};
		   return {showPrimary,showSuccess,showDanger,showWarning,hideNotify};
		 },
		data() {
			return {
				btnPhoneLoading:false,
				btnSubmitLoading:false,
				showPhoneButton:true,
				title: 'Hello',
				userInfo:{
					userWx:{
						nickname:''
					}
				},
				uploadUrl:upload_url,
				updateForm:{
					avatar:'',
					nickname:''
				},
				headers: {
					Authorization: 'Bearer ' + uni.getStorageSync("token"),
					'Content-Type': 'multipart/form-data',
				},
				uploaderData:{
					type:4,
					name:''
				},
			}
		},
		onLoad() {
			this.initData()
		},
		onReachBottom() {
			
		},
		methods: {
			getPhoneNumber(e) {
				console.log(e);
				let code = null
				if(e.detail == undefined) code = e
				else code = e.detail.code
				this.btnPhoneLoading = true
				getBindPhone({phoneCode:code}).then(res => {
					console.log(res);
					this.getInfo()
					this.getCanReqPhone()
				}).catch(err => {
					this.showDanger(err)
				}).finally(res => {
					this.btnPhoneLoading = false
				})
			},
			getInfo() {
				let that = this
				getInfo().then(res => {
					let info = res
					this.$store.commit('login', info.data.user);
					this.$store.commit('setConfig', info.data.config);
					this.freshUserInfo()
				}).catch(err => {
					this.showDanger(err)
				})
			},
			updateProfile(){
				this.btnSubmitLoading = true
				putUpdateProfile(this.updateForm).then(res => {
					console.log(res);
					this.getInfo()
					this.showSuccess("设置成功")
				}).catch(err => {
					this.showDanger(err)
				}).finally(res => {
					this.btnSubmitLoading = false
				})
			},
			oversize(files) {
				console.log(files);
				uni.showToast({
					title: '文件大小超出5MB',
					icon: 'none'
				})
			},
			uploadSuccess(e) {
				console.log(e);
				let data = e.data.data
				data = JSON.parse(data)
				let url = data.data.url
				this.updateForm.avatar = url
				this.userInfo.userWx.avatar = url
			},
			initData(){
				this.freshUserInfo()
				this.getCanReqPhone()
			},
			getCanReqPhone() {
				getCanReqPhone().then(res => {
					console.log(res);
					this.showPhoneButton = res.data
				})
			},
			freshUserInfo() {
				this.userInfo = this.$store.state.userInfo;
				console.log(this.userInfo);
				this.updateForm.avatar = this.userInfo.userWx.avatar
				this.updateForm.nickname = this.userInfo.userWx.nickname
			}
		},
		
	}
</script>
<style lang="scss" scoped>
page {
  background-color: #f2f5fc;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  color: #333;
}

.profile-container {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

/* 顶部个人信息卡片 */
.profile-header {
  position: relative;
  height: 300rpx;
  border-radius: 24rpx;
  margin-bottom: 90rpx;
  background-color: #fff;
  box-shadow: 0 8rpx 20rpx rgba(79, 125, 245, 0.1);
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 160rpx;
  background: linear-gradient(135deg, #4F7DF5, #6C5CE7);
}

.avatar-container {
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 6rpx solid #fff;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.user-info {
  position: absolute;
  bottom: 30rpx;
  left: 0;
  right: 0;
  text-align: center;
}

.nickname-input {
  height: 60rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border: none;
  background: transparent;
  
  :deep(.nut-input__value) {
    text-align: center;
    border: none;
  }
}

.user-status {
  margin-top: 10rpx;
}

/* 信息卡片样式 */
.info-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 0;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #f0f2f5;
  
  .nut-icon {
    margin-right: 16rpx;
    color: #4F7DF5;
  }
  
  text {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
}
}

.card-body {
  padding: 10rpx 0;
}

.phone-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  
  .phone-number {
    font-size: 30rpx;
    color: #333;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1px solid #f8f9fc;
  
  &:last-child {
    border-bottom: none;
}

  .item-label {
    font-size: 28rpx;
    color: #666;
  }
  
  .item-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    
    &.id-value {
      font-size: 24rpx;
      color: #999;
      max-width: 280rpx;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

/* 底部保存按钮 */
.save-btn-container {
  position: fixed;
  bottom: 50rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 10;
}

:deep(.nut-button--primary) {
  background: linear-gradient(135deg, #4F7DF5, #6C5CE7);
  border: none;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  box-shadow: 0 8rpx 20rpx rgba(79, 125, 245, 0.25);
}

:deep(.nut-tag) {
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  
  &[type="primary"] {
    background-color: rgba(79, 125, 245, 0.1);
    color: #4F7DF5;
    border-color: transparent;
}

  &[type="success"] {
    background-color: rgba(66, 185, 131, 0.1);
    color: #42b983;
    border-color: transparent;
  }
  
  &[type="danger"] {
    background-color: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
    border-color: transparent;
}

  &[type="default"] {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999;
    border-color: transparent;
}
}

:deep(.nut-uploader) {
  .nut-uploader__preview-img {
    border-radius: 50%;
    width: 160rpx;
    height: 160rpx;
  }
  
  .nut-uploader__preview {
    margin: 0;
  }
}
</style>