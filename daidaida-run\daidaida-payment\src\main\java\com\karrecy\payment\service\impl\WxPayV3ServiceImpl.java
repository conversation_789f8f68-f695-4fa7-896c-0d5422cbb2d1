package com.karrecy.payment.service.impl;

import com.karrecy.common.exception.PayException;
import com.karrecy.common.utils.BigDecimalUtils;
import com.karrecy.payment.domain.vo.PayedVO;
import com.karrecy.payment.properties.WxPayProperties;
import com.karrecy.payment.service.IPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 微信支付V3实现
 */
@Service("wxPayV3Service")
@Slf4j
public class WxPayV3ServiceImpl implements IPayService {

    private final WxPayProperties wxPayProperties;

    public WxPayV3ServiceImpl(WxPayProperties wxPayProperties) {
        this.wxPayProperties = wxPayProperties;
        log.info("初始化微信支付V3服务实现类（使用测试模式）");
    }

    @Override
    public PayedVO pay(String desc, Long orderId, BigDecimal totalAmount, String openid) {
        try {
            log.info("创建支付订单：描述={}, 订单号={}, 金额={}", desc, orderId, totalAmount);
            
            // 返回模拟支付数据
            PayedVO payedVO = new PayedVO();
            payedVO.setOrderId(orderId);
            payedVO.setAppId(wxPayProperties.getAppId() != null ? wxPayProperties.getAppId() : "test_app_id");
            payedVO.setTimeStamp(String.valueOf(Instant.now().getEpochSecond()));
            payedVO.setNonceStr(java.util.UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
            payedVO.setPackageValue("prepay_id=test_prepay_id_" + orderId);
            payedVO.setSignType("RSA");
            payedVO.setTotalFee(BigDecimalUtils.convertToCents(totalAmount));
            payedVO.setPaySign("test_pay_sign_" + orderId);
            
            log.info("生成支付参数成功：订单号={}", orderId);
            return payedVO;
        } catch (Exception e) {
            log.error("微信支付下单异常", e);
            throw new PayException("微信支付下单失败: " + e.getMessage());
        }
    }

    @Override
    public String payNotifyV3(String notifyData, HttpServletRequest request) {
        try {
            log.info("[测试模式] 收到微信支付通知: {}", notifyData);
            return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
        } catch (Exception e) {
            log.error("处理微信支付通知异常", e);
            return "{\"code\":\"FAIL\",\"message\":\"" + e.getMessage() + "\"}";
        }
    }

    @Override
    public void refund(Long orderId, BigDecimal totalAmount, BigDecimal refundAmount) {
        try {
            log.info("[测试模式] 处理微信支付退款：订单号={}, 退款金额={}", orderId, refundAmount);
            // 测试模式下不执行实际退款操作
        } catch (Exception e) {
            log.error("微信支付退款异常", e);
            throw new PayException("微信支付退款失败: " + e.getMessage());
        }
    }

    @Override
    public String refundNotify(String notifyData, HttpServletRequest request) {
        try {
            log.info("[测试模式] 收到微信支付退款通知: {}", notifyData);
            return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
        } catch (Exception e) {
            log.error("处理微信支付退款通知异常", e);
            return "{\"code\":\"FAIL\",\"message\":\"" + e.getMessage() + "\"}";
        }
    }
} 