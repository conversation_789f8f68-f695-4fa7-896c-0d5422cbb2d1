// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"path":"/welcome","name":"welcome","icon":"smile","parentId":"ant-design-pro-layout","id":"3"},"4":{"path":"/admin","name":"admin","icon":"crown","access":"canAdmin","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/admin","redirect":"/admin/sub-page","parentId":"4","id":"5"},"6":{"path":"/admin/sub-page","name":"sub-page","parentId":"4","id":"6"},"7":{"name":"user-manage","icon":"user","path":"/userManage","parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/userManage/pc","name":"pc-user","perms":"system:userpc:view","access":"routeFilter","parentId":"7","id":"8"},"9":{"path":"/userManage/xcx","name":"mini-program-user","perms":"system:userwx:view","access":"routeFilter","parentId":"7","id":"9"},"10":{"name":"system","icon":"setting","path":"/system","parentId":"ant-design-pro-layout","id":"10"},"11":{"path":"/system/config","name":"configuration","perms":"system:system:config:view","access":"routeFilter","parentId":"10","id":"11"},"12":{"path":"/system/permission","name":"permission","perms":"system:system:perms:view","access":"routeFilter","parentId":"10","id":"12"},"13":{"path":"/system/monitor","name":"monitor","perms":"system:system:monitor:view","access":"routeFilter","parentId":"10","id":"13"},"14":{"path":"/system/carousel","name":"carousel","perms":"system:system:carousel:view","access":"routeFilter","parentId":"10","id":"14"},"15":{"name":"order","icon":"fileText","path":"/order","parentId":"ant-design-pro-layout","id":"15"},"16":{"path":"/order","perms":"order:order:view","access":"routeFilter","parentId":"15","id":"16"},"17":{"path":"/order/detail/:id","perms":"order:detail:view","access":"routeFilter","parentId":"15","id":"17"},"18":{"name":"tag","icon":"book","path":"/tag","perms":"order:tag:view","access":"routeFilter","parentId":"ant-design-pro-layout","id":"18"},"19":{"name":"workorder","icon":"exception","path":"/workorder","parentId":"ant-design-pro-layout","id":"19"},"20":{"path":"/workorder/runnerApply","name":"runner-application","perms":"runnerApply:view","access":"routeFilter","parentId":"19","id":"20"},"21":{"path":"/workorder/orderAppeal","name":"order-appeal","perms":"order:appeal:view","access":"routeFilter","parentId":"19","id":"21"},"22":{"name":"capital","icon":"propertySafety","path":"/capital","parentId":"ant-design-pro-layout","id":"22"},"23":{"path":"/capital/wallet","name":"wallet","perms":"payment:withdraw:view","access":"routeFilter","parentId":"22","id":"23"},"24":{"path":"/capital/withdrawal","name":"withdrawal","perms":"payment:recode:view","access":"routeFilter","parentId":"22","id":"24"},"25":{"path":"/capital/flow","name":"capital-flow","perms":"payment:flow:view","access":"routeFilter","parentId":"22","id":"25"},"26":{"path":"/capital/mywallet","name":"my-wallet","perms":"payment:mywallet:view","access":"routeFilter","parentId":"22","id":"26"},"27":{"path":"/capital/myflow","name":"my-flow","perms":"payment:myflow:view","access":"routeFilter","parentId":"22","id":"27"},"28":{"name":"school","icon":"home","path":"/school","parentId":"ant-design-pro-layout","id":"28"},"29":{"path":"/school/campus","name":"campus","perms":"address:school:view","access":"routeFilter","parentId":"28","id":"29"},"30":{"path":"/school/region","name":"region","perms":"address:region:view","access":"routeFilter","parentId":"28","id":"30"},"31":{"name":"oss","icon":"star","path":"/oss","parentId":"ant-design-pro-layout","id":"31"},"32":{"path":"/oss/manage","name":"oss-manage","perms":"oss:oss:view","access":"routeFilter","parentId":"31","id":"32"},"33":{"path":"/oss/config","name":"oss-config","perms":"oss:config:view","access":"routeFilter","parentId":"31","id":"33"},"34":{"name":"个人中心","icon":"star","path":"/profile","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"34"},"35":{"path":"/","redirect":"/welcome","parentId":"ant-design-pro-layout","id":"35"},"36":{"path":"*","layout":false,"id":"36"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true},"umi/plugin/openapi":{"path":"/umi/plugin/openapi","id":"umi/plugin/openapi"}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__User__Login__index" */'@/pages/User/Login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__Welcome" */'@/pages/Welcome.tsx')),
'4': React.lazy(() => import('./EmptyRoute')),
'5': React.lazy(() => import('./EmptyRoute')),
'6': React.lazy(() => import(/* webpackChunkName: "p__Admin" */'@/pages/Admin.tsx')),
'7': React.lazy(() => import('./EmptyRoute')),
'8': React.lazy(() => import(/* webpackChunkName: "p__userManage__pc__index" */'@/pages/userManage/pc/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__userManage__xcx__index" */'@/pages/userManage/xcx/index.tsx')),
'10': React.lazy(() => import('./EmptyRoute')),
'11': React.lazy(() => import(/* webpackChunkName: "p__system__config__index" */'@/pages/system/config/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__system__permission__index" */'@/pages/system/permission/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__system__monitor__index" */'@/pages/system/monitor/index.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__system__carousel__index" */'@/pages/system/carousel/index.tsx')),
'15': React.lazy(() => import('./EmptyRoute')),
'16': React.lazy(() => import(/* webpackChunkName: "p__order__index" */'@/pages/order/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__order__detail__index" */'@/pages/order/detail/index.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__tag__index" */'@/pages/tag/index.tsx')),
'19': React.lazy(() => import('./EmptyRoute')),
'20': React.lazy(() => import(/* webpackChunkName: "p__workorder__runnerApply__index" */'@/pages/workorder/runnerApply/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__workorder__orderAppeal__index" */'@/pages/workorder/orderAppeal/index.tsx')),
'22': React.lazy(() => import('./EmptyRoute')),
'23': React.lazy(() => import(/* webpackChunkName: "p__capital__wallet__index" */'@/pages/capital/wallet/index.tsx')),
'24': React.lazy(() => import(/* webpackChunkName: "p__capital__withdrawal__index" */'@/pages/capital/withdrawal/index.tsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__capital__flow__index" */'@/pages/capital/flow/index.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__capital__mywallet__index" */'@/pages/capital/mywallet/index.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__capital__myflow__index" */'@/pages/capital/myflow/index.tsx')),
'28': React.lazy(() => import('./EmptyRoute')),
'29': React.lazy(() => import(/* webpackChunkName: "p__school__campus__index" */'@/pages/school/campus/index.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__school__region__index" */'@/pages/school/region/index.tsx')),
'31': React.lazy(() => import('./EmptyRoute')),
'32': React.lazy(() => import(/* webpackChunkName: "p__oss__manage__index" */'@/pages/oss/manage/index.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__oss__config__index" */'@/pages/oss/config/index.tsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__User__profile__index" */'@/pages/User/profile/index.tsx')),
'35': React.lazy(() => import('./EmptyRoute')),
'36': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'C:/Users/<USER>/Desktop/fuu-run-master - test/daidaida-run-admin/src/.umi/plugin-layout/Layout.tsx')),
'umi/plugin/openapi': React.lazy(() => import(/* webpackChunkName: "umi__plugin-openapi__openapi" */'C:/Users/<USER>/Desktop/fuu-run-master - test/daidaida-run-admin/src/.umi/plugin-openapi/openapi.tsx')),
},
  };
}
